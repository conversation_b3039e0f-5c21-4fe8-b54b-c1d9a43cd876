// pages/index.tsx
import Link from 'next/link';
import React from 'react';
import SidebarLayout from '../components/SidebarLayoutSimple';
import type { ReactElement } from 'react';
import type { NextPageWithLayout } from './_app';

const HomePage: NextPageWithLayout = () => {
  const colors = {
    primary: '#F97316',
    text: {
      primary: '#1F2937',
      secondary: '#6B7280',
      tertiary: '#9CA3AF'
    },
    border: '#E5E7EB',
    surface: '#FFFFFF',
    background: '#F8F9FA'
  };

  return (
    <div style={{ padding: '32px', height: '100vh', overflow: 'auto' }}>
      {/* Clean Header */}
      <div style={{ marginBottom: '32px' }}>
        <h1 style={{
          color: colors.text.primary,
          margin: 0,
          fontSize: '28px',
          fontWeight: '600',
          letterSpacing: '-0.5px',
          marginBottom: '8px'
        }}>
          Briefing Room
        </h1>
        <p style={{
          color: colors.text.secondary,
          fontSize: '16px',
          margin: 0,
          fontWeight: '400'
        }}>
          Your daily mission control center
        </p>
      </div>

      {/* Constrained Content Width */}
      <div style={{
        maxWidth: '800px',
        margin: '0 auto'
      }}>
        {/* Today's Mission Card */}
        <div style={{
          background: colors.surface,
          borderRadius: '16px',
          padding: '24px',
          marginBottom: '24px',
          boxShadow: `0 4px 16px rgba(0, 0, 0, 0.04)`,
          border: `1px solid ${colors.border}`
        }}>
        <div style={{ marginBottom: '16px' }}>
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '12px' }}>
            <h2 style={{
              color: colors.text.primary,
              margin: 0,
              fontSize: '20px',
              fontWeight: '600'
            }}>
              Today's Mission
            </h2>
            <div style={{
              padding: '4px 8px',
              background: `${colors.primary}15`,
              borderRadius: '6px',
              color: colors.primary,
              fontSize: '11px',
              fontWeight: '600',
              textTransform: 'uppercase',
              letterSpacing: '0.5px'
            }}>
              AI Generated
            </div>
          </div>

          <p style={{
            color: colors.text.secondary,
            fontSize: '16px',
            lineHeight: '1.5',
            margin: 0,
            marginBottom: '20px'
          }}>
            Focus on engaging with your audience about AI productivity tools. Your recent posts about automation got 3x more engagement. Consider sharing a behind-the-scenes story about how AI helps your workflow.
          </p>

          {/* Performance Summary */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(3, 1fr)',
            gap: '16px',
            marginBottom: '20px'
          }}>
            {[
              { label: 'Engagement Rate', value: '+24%' },
              { label: 'New Followers', value: '127' },
              { label: 'Content Score', value: '8.9/10' }
            ].map((stat, index) => (
              <div key={index} style={{
                background: `${colors.primary}08`,
                borderRadius: '12px',
                padding: '16px',
                textAlign: 'center',
                border: `1px solid ${colors.primary}15`
              }}>
                <div style={{
                  fontSize: '20px',
                  fontWeight: '700',
                  color: colors.text.primary,
                  marginBottom: '4px'
                }}>
                  {stat.value}
                </div>
                <div style={{
                  fontSize: '13px',
                  color: colors.text.tertiary,
                  fontWeight: '500'
                }}>
                  {stat.label}
                </div>
              </div>
            ))}
          </div>

          {/* Action Buttons */}
          <div style={{
            display: 'flex',
            gap: '12px',
            flexWrap: 'wrap'
          }}>
            <Link href="/meeting" style={{ textDecoration: 'none' }}>
              <button style={{
                padding: '12px 20px',
                background: colors.primary,
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                fontSize: '14px',
                fontWeight: '600',
                cursor: 'pointer',
                transition: 'all 0.2s ease'
              }}>
                Join Call
              </button>
            </Link>

            <button style={{
              padding: '12px 20px',
              background: colors.surface,
              color: colors.text.primary,
              border: `1px solid ${colors.border}`,
              borderRadius: '8px',
              fontSize: '14px',
              fontWeight: '600',
              cursor: 'pointer',
              transition: 'all 0.2s ease'
            }}>
              Ask Mentor
            </button>

            <Link href="/tweet-center" style={{ textDecoration: 'none' }}>
              <button style={{
                padding: '12px 20px',
                background: colors.surface,
                color: colors.text.primary,
                border: `1px solid ${colors.border}`,
                borderRadius: '8px',
                fontSize: '14px',
                fontWeight: '600',
                cursor: 'pointer',
                transition: 'all 0.2s ease'
              }}>
                Create Content
              </button>
            </Link>
          </div>
        </div>
      </div>

        {/* AI Mentor Section */}
        <div style={{
          background: colors.surface,
          borderRadius: '16px',
          padding: '24px',
          boxShadow: `0 4px 16px rgba(0, 0, 0, 0.04)`,
          border: `1px solid ${colors.border}`
        }}>
          <div style={{ display: 'flex', alignItems: 'flex-start', gap: '16px' }}>
            <div style={{
              width: '48px',
              height: '48px',
              borderRadius: '12px',
              background: `${colors.primary}15`,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              position: 'relative',
              flexShrink: 0
            }}>
              <div style={{
                width: '24px',
                height: '24px',
                borderRadius: '6px',
                background: colors.primary
              }} />
              <div style={{
                position: 'absolute',
                bottom: '2px',
                right: '2px',
                width: '12px',
                height: '12px',
                borderRadius: '50%',
                background: '#00E676',
                border: '2px solid white'
              }} />
            </div>
            <div style={{ flex: 1 }}>
              <h3 style={{
                color: colors.text.primary,
                margin: 0,
                fontSize: '16px',
                fontWeight: '600',
                marginBottom: '8px'
              }}>
                AI Mentor
              </h3>
              <p style={{
                color: colors.text.secondary,
                margin: 0,
                fontSize: '15px',
                lineHeight: '1.5'
              }}>
                Ready to help you create content that resonates. What's on your mind today?
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

HomePage.getLayout = function getLayout(page: ReactElement) {
  return (
    <SidebarLayout>
      {page}
    </SidebarLayout>
  );
};

export default HomePage;