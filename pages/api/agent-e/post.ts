// pages/api/agent-e/post.ts
import type { NextApiRequest, NextApiResponse } from 'next';

interface PostRequest {
  content: string;
  action: 'post' | 'schedule' | 'improve' | 'thread';
  scheduleTime?: string;
  customPrompt?: string;
}

interface PostResponse {
  success: boolean;
  message: string;
  tweetId?: string;
  scheduledFor?: string;
  improvedContent?: string;
  threadContent?: string[];
  error?: string;
}

// Simulate X API posting (replace with actual X API integration)
const postToX = async (content: string): Promise<{ success: boolean; tweetId?: string; error?: string }> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // Simulate success/failure
  if (content.length > 280) {
    return { success: false, error: 'Tweet too long' };
  }
  
  return { 
    success: true, 
    tweetId: `tweet_${Date.now()}_${Math.random().toString(36).substr(2, 9)}` 
  };
};

// AI content improvement using OpenAI
const improveContent = async (content: string, customPrompt?: string): Promise<string> => {
  const prompt = customPrompt || `
    Improve this social media post for better engagement:
    "${content}"
    
    Make it more engaging, add relevant hashtags, and optimize for X's algorithm.
    Keep it under 280 characters and maintain the original message.
  `;

  try {
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4',
        messages: [
          {
            role: 'system',
            content: 'You are an expert social media manager specializing in X (Twitter) content optimization.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 300,
        temperature: 0.7,
      }),
    });

    const data = await response.json();
    return data.choices[0]?.message?.content || content;
  } catch (error) {
    console.error('Error improving content:', error);
    return content;
  }
};

// Convert content to thread
const createThread = async (content: string): Promise<string[]> => {
  if (content.length <= 280) {
    return [content];
  }

  try {
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4',
        messages: [
          {
            role: 'system',
            content: 'You are an expert at breaking down long content into engaging Twitter threads. Each tweet should be under 280 characters and flow naturally.'
          },
          {
            role: 'user',
            content: `Break this content into a Twitter thread:\n\n"${content}"\n\nReturn as a JSON array of strings, each under 280 characters.`
          }
        ],
        max_tokens: 800,
        temperature: 0.7,
      }),
    });

    const data = await response.json();
    const threadText = data.choices[0]?.message?.content || '';
    
    try {
      const thread = JSON.parse(threadText);
      return Array.isArray(thread) ? thread : [content];
    } catch {
      // Fallback: simple split by sentences
      const sentences = content.split('. ');
      const thread: string[] = [];
      let currentTweet = '';
      
      for (const sentence of sentences) {
        if ((currentTweet + sentence + '. ').length <= 280) {
          currentTweet += sentence + '. ';
        } else {
          if (currentTweet) thread.push(currentTweet.trim());
          currentTweet = sentence + '. ';
        }
      }
      
      if (currentTweet) thread.push(currentTweet.trim());
      return thread.length > 0 ? thread : [content];
    }
  } catch (error) {
    console.error('Error creating thread:', error);
    return [content];
  }
};

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<PostResponse>
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    const { content, action, scheduleTime, customPrompt }: PostRequest = req.body;

    if (!content || !action) {
      return res.status(400).json({ 
        success: false, 
        message: 'Content and action are required' 
      });
    }

    switch (action) {
      case 'post':
        const postResult = await postToX(content);
        if (postResult.success) {
          return res.status(200).json({
            success: true,
            message: 'Successfully posted to X!',
            tweetId: postResult.tweetId
          });
        } else {
          return res.status(400).json({
            success: false,
            message: postResult.error || 'Failed to post to X'
          });
        }

      case 'schedule':
        // In a real implementation, you'd store this in a database and use a cron job
        return res.status(200).json({
          success: true,
          message: `Scheduled for ${scheduleTime}`,
          scheduledFor: scheduleTime
        });

      case 'improve':
        const improvedContent = await improveContent(content, customPrompt);
        return res.status(200).json({
          success: true,
          message: 'Content improved successfully',
          improvedContent
        });

      case 'thread':
        const threadContent = await createThread(content);
        return res.status(200).json({
          success: true,
          message: 'Thread created successfully',
          threadContent
        });

      default:
        return res.status(400).json({
          success: false,
          message: 'Invalid action'
        });
    }
  } catch (error) {
    console.error('Agent E API error:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
