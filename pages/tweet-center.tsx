// pages/tweet-center.tsx
import React, { useState, useRef, useEffect } from 'react';
import SidebarLayout from '../components/SidebarLayout';
import { Sparkles, Type, AlignLeft, AlignCenter, Bot } from 'lucide-react';
import AgentEChat from '../components/AgentEChat';
import type { ReactElement } from 'react';
import type { NextPageWithLayout } from './_app';

const TweetCenterPage: NextPageWithLayout = () => {
  const [content, setContent] = useState('');
  const [aiSuggestion, setAiSuggestion] = useState('');
  const [showSuggestion, setShowSuggestion] = useState(false);
  const [aiEnabled, setAiEnabled] = useState(true);
  const [formatMode, setFormatMode] = useState<'thread' | 'single'>('single');
  const [agentEOpen, setAgentEOpen] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const colors = {
    primary: '#FF6B35',
    primaryLight: '#FF8A65',
    text: {
      primary: '#2D1B14',
      secondary: '#5D4037',
      tertiary: '#8D6E63'
    },
    border: '#F5E6D3',
    borderHover: '#E8D5C4',
    surface: '#FFFFFF',
    surfaceHover: '#F9F7F4',
    warmGlow: '#FFE0B2',
    paper: '#FEFEFE'
  };

  // Intelligent AI prediction based on content context
  const generateContextualSuggestion = (text: string) => {
    const lowerText = text.toLowerCase();

    // Trading/Finance context
    if (lowerText.includes('trading') || lowerText.includes('stocks') || lowerText.includes('crypto')) {
      return ' - risk management is everything. Never trade with money you can\'t afford to lose.';
    }

    // AI/Tech context
    if (lowerText.includes('ai') || lowerText.includes('artificial intelligence') || lowerText.includes('machine learning')) {
      return ' is transforming how we work. The key is learning to collaborate with AI, not compete against it.';
    }

    // Productivity context
    if (lowerText.includes('productivity') || lowerText.includes('workflow') || lowerText.includes('efficiency')) {
      return ': 1) Single-task focus 2) Time blocking 3) Automate repetitive work. Small changes, big results.';
    }

    // Building/Entrepreneurship context
    if (lowerText.includes('building') || lowerText.includes('startup') || lowerText.includes('business')) {
      return ' in public. Share your journey, failures, and wins. Your audience wants authenticity, not perfection.';
    }

    // Learning/Growth context
    if (lowerText.includes('learning') || lowerText.includes('skill') || lowerText.includes('growth')) {
      return ' - the best investment you can make is in yourself. Consistency beats intensity every time.';
    }

    // Default contextual suggestions
    const endings = [
      ' - here\'s what I learned from 5 years of experience.',
      '. The biggest mistake I see people make is...',
      '. Here are 3 things that changed everything for me:',
      ' - and it completely shifted my perspective.',
      '. If I started over today, I\'d focus on this first.'
    ];

    return endings[Math.floor(Math.random() * endings.length)];
  };

  // AI prediction with context awareness
  useEffect(() => {
    if (content.length > 15 && aiEnabled) {
      const timer = setTimeout(() => {
        const suggestion = generateContextualSuggestion(content);
        setAiSuggestion(suggestion);
        setShowSuggestion(true);
      }, 800);
      return () => clearTimeout(timer);
    } else {
      setShowSuggestion(false);
    }
  }, [content, aiEnabled]);

  const handleTabPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Tab' && showSuggestion) {
      e.preventDefault();
      setContent(content + aiSuggestion);
      setShowSuggestion(false);
      setAiSuggestion('');
    }
  };

  const autoFormat = () => {
    if (content.length > 280) {
      // Convert to thread format
      const sentences = content.split('. ');
      const threads: string[] = [];
      let currentThread = '';

      sentences.forEach(sentence => {
        if ((currentThread + sentence + '. ').length <= 280) {
          currentThread += sentence + '. ';
        } else {
          if (currentThread) threads.push(currentThread.trim());
          currentThread = sentence + '. ';
        }
      });

      if (currentThread) threads.push(currentThread.trim());
      setContent(threads.join('\n\n'));
      setFormatMode('thread');
    }
  };

  return (
    <div style={{
      padding: '40px 60px',
      height: '100vh',
      overflow: 'auto',
      background: colors.paper,
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center'
    }}>
      {/* Minimal Header */}
      <div style={{
        width: '100%',
        maxWidth: '800px',
        marginBottom: '40px',
        textAlign: 'center'
      }}>
        <h1 style={{
          color: colors.text.primary,
          margin: 0,
          fontSize: '32px',
          fontWeight: '300',
          letterSpacing: '-1px',
          marginBottom: '12px',
          fontFamily: 'Georgia, serif'
        }}>
          Drafting Desk
        </h1>

        {/* Clean Toolbar */}
        <div style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          gap: '16px',
          marginTop: '24px'
        }}>
          {/* AI Toggle */}
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            padding: '6px 12px',
            background: aiEnabled ? `${colors.primary}15` : colors.surface,
            borderRadius: '20px',
            border: `1px solid ${aiEnabled ? colors.primary : colors.border}`,
            cursor: 'pointer',
            transition: 'all 0.2s ease'
          }}
          onClick={() => setAiEnabled(!aiEnabled)}
          >
            <Sparkles size={14} color={aiEnabled ? colors.primary : colors.text.tertiary} />
            <span style={{
              fontSize: '13px',
              fontWeight: '500',
              color: aiEnabled ? colors.primary : colors.text.tertiary
            }}>
              AI Assistant
            </span>
          </div>

          {/* Auto Format Button */}
          <button
            onClick={autoFormat}
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '6px',
              padding: '6px 12px',
              background: colors.surface,
              border: `1px solid ${colors.border}`,
              borderRadius: '20px',
              fontSize: '13px',
              fontWeight: '500',
              color: colors.text.secondary,
              cursor: 'pointer',
              transition: 'all 0.2s ease'
            }}
          >
            <Type size={14} />
            Auto Format
          </button>

          {/* Format Mode Indicator */}
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '6px',
            padding: '6px 12px',
            background: formatMode === 'thread' ? `${colors.primary}15` : colors.surface,
            borderRadius: '20px',
            border: `1px solid ${formatMode === 'thread' ? colors.primary : colors.border}`
          }}>
            {formatMode === 'thread' ? <AlignLeft size={14} color={colors.primary} /> : <AlignCenter size={14} color={colors.text.tertiary} />}
            <span style={{
              fontSize: '13px',
              fontWeight: '500',
              color: formatMode === 'thread' ? colors.primary : colors.text.tertiary
            }}>
              {formatMode === 'thread' ? 'Thread' : 'Single'}
            </span>
          </div>
        </div>
      </div>

      {/* Seamless Writing Interface */}
      <div style={{
        width: '100%',
        maxWidth: '900px',
        background: 'transparent',
        position: 'relative',
        minHeight: '500px',
        display: 'flex',
        flexDirection: 'column'
      }}>

        {/* Inline Writing Area with Tab Completion */}
        <div style={{
          flex: 1,
          position: 'relative',
          padding: '40px 60px',
          minHeight: '450px'
        }}>
          {/* AI Suggestion Overlay - Inline Gray Text */}
          {showSuggestion && aiEnabled && (
            <div style={{
              position: 'absolute',
              top: '40px',
              left: '60px',
              right: '60px',
              bottom: '40px',
              pointerEvents: 'none',
              zIndex: 1,
              overflow: 'hidden'
            }}>
              <div className="sf-pro" style={{
                fontSize: '20px',
                lineHeight: '1.7',
                color: 'transparent',
                whiteSpace: 'pre-wrap',
                wordWrap: 'break-word',
                letterSpacing: '0.3px',
                fontWeight: '400'
              }}>
                {content}
                <span style={{
                  color: '#9CA3AF',
                  opacity: 0.6,
                  fontStyle: 'normal'
                }}>
                  {aiSuggestion}
                </span>
              </div>
            </div>
          )}

          {/* Main textarea */}
          <textarea
            ref={textareaRef}
            value={content}
            onChange={(e) => setContent(e.target.value)}
            onKeyDown={handleTabPress}
            placeholder={aiEnabled ? 'Start writing and I\'ll help you continue...' : 'What\'s on your mind?'}
            className="sf-pro"
            style={{
              width: '100%',
              height: '100%',
              minHeight: '400px',
              padding: '0',
              border: 'none',
              background: 'transparent',
              fontSize: '20px',
              lineHeight: '1.7',
              color: colors.text.primary,
              resize: 'none',
              outline: 'none',
              letterSpacing: '0.3px',
              position: 'relative',
              zIndex: 2,
              fontWeight: '400'
            }}
          />
        </div>

        {/* Minimal Action Bar */}
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          padding: '0 60px 40px',
          marginTop: '20px'
        }}>
          <div className="sf-pro" style={{
            fontSize: '14px',
            color: colors.text.secondary,
            fontWeight: '400',
            opacity: 0.7
          }}>
            {content.length} characters
          </div>

          <div style={{ display: 'flex', gap: '12px' }}>
            <button
              onClick={() => setAgentEOpen(true)}
              className="sf-pro"
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                padding: '12px 20px',
                background: `linear-gradient(135deg, rgba(138, 43, 226, 0.1) 0%, rgba(75, 0, 130, 0.1) 100%)`,
                border: `1px solid rgba(138, 43, 226, 0.3)`,
                borderRadius: '10px',
                color: '#8A2BE2',
                fontSize: '14px',
                fontWeight: '600',
                cursor: 'pointer',
                transition: 'all 0.2s ease'
              }}
              onMouseEnter={(e) => {
                const target = e.target as HTMLButtonElement;
                target.style.background = `linear-gradient(135deg, rgba(138, 43, 226, 0.15) 0%, rgba(75, 0, 130, 0.15) 100%)`;
                target.style.transform = 'translateY(-1px)';
              }}
              onMouseLeave={(e) => {
                const target = e.target as HTMLButtonElement;
                target.style.background = `linear-gradient(135deg, rgba(138, 43, 226, 0.1) 0%, rgba(75, 0, 130, 0.1) 100%)`;
                target.style.transform = 'translateY(0)';
              }}
            >
              <Bot size={16} />
              Agent E
            </button>

            <button
              onClick={() => setContent('')}
              className="sf-pro"
              style={{
                padding: '12px 24px',
                background: 'transparent',
                border: `1px solid ${colors.border}`,
                borderRadius: '10px',
                color: colors.text.secondary,
                fontSize: '14px',
                fontWeight: '500',
                cursor: 'pointer',
                transition: 'all 0.2s ease'
              }}
              onMouseEnter={(e) => {
                const target = e.target as HTMLButtonElement;
                target.style.background = colors.surfaceHover;
                target.style.borderColor = colors.borderHover;
              }}
              onMouseLeave={(e) => {
                const target = e.target as HTMLButtonElement;
                target.style.background = 'transparent';
                target.style.borderColor = colors.border;
              }}
            >
              Save Draft
            </button>

            <button
              onClick={() => {
                // Publish logic here
                console.log('Publishing:', content);
              }}
              className="sf-pro"
              style={{
                padding: '12px 28px',
                background: `linear-gradient(135deg, ${colors.primary} 0%, ${colors.primaryLight} 100%)`,
                border: 'none',
                borderRadius: '10px',
                color: 'white',
                fontSize: '14px',
                fontWeight: '600',
                cursor: 'pointer',
                boxShadow: `0 4px 12px ${colors.primary}30`,
                transition: 'all 0.2s ease'
              }}
              onMouseEnter={(e) => {
                const target = e.target as HTMLButtonElement;
                target.style.transform = 'translateY(-1px)';
                target.style.boxShadow = `0 6px 16px ${colors.primary}40`;
              }}
              onMouseLeave={(e) => {
                const target = e.target as HTMLButtonElement;
                target.style.transform = 'translateY(0)';
                target.style.boxShadow = `0 4px 12px ${colors.primary}30`;
              }}
            >
              Publish
            </button>
          </div>
        </div>
      </div>

      {/* Floating Agent E Button */}
      {content.length > 10 && !agentEOpen && (
        <div style={{
          position: 'fixed',
          bottom: '30px',
          right: '30px',
          zIndex: 999
        }}>
          <button
            onClick={() => setAgentEOpen(true)}
            className="sf-pro"
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '12px',
              padding: '16px 24px',
              background: `linear-gradient(135deg, #8A2BE2 0%, #4B0082 100%)`,
              color: 'white',
              border: 'none',
              borderRadius: '50px',
              fontSize: '16px',
              fontWeight: '600',
              cursor: 'pointer',
              boxShadow: '0 8px 32px rgba(138, 43, 226, 0.4)',
              transition: 'all 0.3s ease',
              animation: 'pulse 2s infinite'
            }}
            onMouseEnter={(e) => {
              const target = e.target as HTMLButtonElement;
              target.style.transform = 'translateY(-2px) scale(1.05)';
              target.style.boxShadow = '0 12px 40px rgba(138, 43, 226, 0.5)';
            }}
            onMouseLeave={(e) => {
              const target = e.target as HTMLButtonElement;
              target.style.transform = 'translateY(0) scale(1)';
              target.style.boxShadow = '0 8px 32px rgba(138, 43, 226, 0.4)';
            }}
          >
            <Bot size={20} />
            <span>Ask Agent E</span>
            <Sparkles size={16} style={{ opacity: 0.8 }} />
          </button>
        </div>
      )}

      {/* Agent E Chat */}
      <AgentEChat
        isOpen={agentEOpen}
        onClose={() => setAgentEOpen(false)}
        currentContent={content}
      />

      <style jsx>{`
        @keyframes pulse {
          0%, 100% { opacity: 1; }
          50% { opacity: 0.8; }
        }
      `}</style>
    </div>
  );
};

TweetCenterPage.getLayout = function getLayout(page: ReactElement) {
  return (
    <SidebarLayout>
      {page}
    </SidebarLayout>
  );
};

export default TweetCenterPage;