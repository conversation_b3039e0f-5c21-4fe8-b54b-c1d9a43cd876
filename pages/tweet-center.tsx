// pages/tweet-center.tsx
import React, { useState, useRef, useEffect } from 'react';
import SidebarLayout from '../components/SidebarLayout';
import { Wand2, RotateCcw, Sparkles, Type, AlignLeft, AlignCenter } from 'lucide-react';
import type { ReactElement } from 'react';
import type { NextPageWithLayout } from './_app';

const TweetCenterPage: NextPageWithLayout = () => {
  const [content, setContent] = useState('');
  const [aiSuggestion, setAiSuggestion] = useState('');
  const [showSuggestion, setShowSuggestion] = useState(false);
  const [aiEnabled, setAiEnabled] = useState(true);
  const [formatMode, setFormatMode] = useState<'thread' | 'single'>('single');
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const colors = {
    primary: '#FF6B35',
    primaryLight: '#FF8A65',
    text: {
      primary: '#2D1B14',
      secondary: '#5D4037',
      tertiary: '#8D6E63'
    },
    border: '#F5E6D3',
    surface: '#FFFFFF',
    warmGlow: '#FFE0B2',
    paper: '#FEFEFE'
  };

  // Intelligent AI prediction based on content context
  const generateContextualSuggestion = (text: string) => {
    const lowerText = text.toLowerCase();

    // Trading/Finance context
    if (lowerText.includes('trading') || lowerText.includes('stocks') || lowerText.includes('crypto')) {
      return ' - risk management is everything. Never trade with money you can\'t afford to lose.';
    }

    // AI/Tech context
    if (lowerText.includes('ai') || lowerText.includes('artificial intelligence') || lowerText.includes('machine learning')) {
      return ' is transforming how we work. The key is learning to collaborate with AI, not compete against it.';
    }

    // Productivity context
    if (lowerText.includes('productivity') || lowerText.includes('workflow') || lowerText.includes('efficiency')) {
      return ': 1) Single-task focus 2) Time blocking 3) Automate repetitive work. Small changes, big results.';
    }

    // Building/Entrepreneurship context
    if (lowerText.includes('building') || lowerText.includes('startup') || lowerText.includes('business')) {
      return ' in public. Share your journey, failures, and wins. Your audience wants authenticity, not perfection.';
    }

    // Learning/Growth context
    if (lowerText.includes('learning') || lowerText.includes('skill') || lowerText.includes('growth')) {
      return ' - the best investment you can make is in yourself. Consistency beats intensity every time.';
    }

    // Default contextual suggestions
    const endings = [
      ' - here\'s what I learned from 5 years of experience.',
      '. The biggest mistake I see people make is...',
      '. Here are 3 things that changed everything for me:',
      ' - and it completely shifted my perspective.',
      '. If I started over today, I\'d focus on this first.'
    ];

    return endings[Math.floor(Math.random() * endings.length)];
  };

  // AI prediction with context awareness
  useEffect(() => {
    if (content.length > 15 && aiEnabled) {
      const timer = setTimeout(() => {
        const suggestion = generateContextualSuggestion(content);
        setAiSuggestion(suggestion);
        setShowSuggestion(true);
      }, 800);
      return () => clearTimeout(timer);
    } else {
      setShowSuggestion(false);
    }
  }, [content, aiEnabled]);

  const handleTabPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Tab' && showSuggestion) {
      e.preventDefault();
      setContent(content + aiSuggestion);
      setShowSuggestion(false);
      setAiSuggestion('');
    }
  };

  const autoFormat = () => {
    if (content.length > 280) {
      // Convert to thread format
      const sentences = content.split('. ');
      const threads: string[] = [];
      let currentThread = '';

      sentences.forEach(sentence => {
        if ((currentThread + sentence + '. ').length <= 280) {
          currentThread += sentence + '. ';
        } else {
          if (currentThread) threads.push(currentThread.trim());
          currentThread = sentence + '. ';
        }
      });

      if (currentThread) threads.push(currentThread.trim());
      setContent(threads.join('\n\n'));
      setFormatMode('thread');
    }
  };

  return (
    <div style={{
      padding: '40px 60px',
      height: '100vh',
      overflow: 'auto',
      background: colors.paper,
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center'
    }}>
      {/* Minimal Header */}
      <div style={{
        width: '100%',
        maxWidth: '800px',
        marginBottom: '40px',
        textAlign: 'center'
      }}>
        <h1 style={{
          color: colors.text.primary,
          margin: 0,
          fontSize: '32px',
          fontWeight: '300',
          letterSpacing: '-1px',
          marginBottom: '12px',
          fontFamily: 'Georgia, serif'
        }}>
          Drafting Desk
        </h1>

        {/* Clean Toolbar */}
        <div style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          gap: '16px',
          marginTop: '24px'
        }}>
          {/* AI Toggle */}
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            padding: '6px 12px',
            background: aiEnabled ? `${colors.primary}15` : colors.surface,
            borderRadius: '20px',
            border: `1px solid ${aiEnabled ? colors.primary : colors.border}`,
            cursor: 'pointer',
            transition: 'all 0.2s ease'
          }}
          onClick={() => setAiEnabled(!aiEnabled)}
          >
            <Sparkles size={14} color={aiEnabled ? colors.primary : colors.text.tertiary} />
            <span style={{
              fontSize: '13px',
              fontWeight: '500',
              color: aiEnabled ? colors.primary : colors.text.tertiary
            }}>
              AI Assistant
            </span>
          </div>

          {/* Auto Format Button */}
          <button
            onClick={autoFormat}
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '6px',
              padding: '6px 12px',
              background: colors.surface,
              border: `1px solid ${colors.border}`,
              borderRadius: '20px',
              fontSize: '13px',
              fontWeight: '500',
              color: colors.text.secondary,
              cursor: 'pointer',
              transition: 'all 0.2s ease'
            }}
          >
            <Type size={14} />
            Auto Format
          </button>

          {/* Format Mode Indicator */}
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '6px',
            padding: '6px 12px',
            background: formatMode === 'thread' ? `${colors.primary}15` : colors.surface,
            borderRadius: '20px',
            border: `1px solid ${formatMode === 'thread' ? colors.primary : colors.border}`
          }}>
            {formatMode === 'thread' ? <AlignLeft size={14} color={colors.primary} /> : <AlignCenter size={14} color={colors.text.tertiary} />}
            <span style={{
              fontSize: '13px',
              fontWeight: '500',
              color: formatMode === 'thread' ? colors.primary : colors.text.tertiary
            }}>
              {formatMode === 'thread' ? 'Thread' : 'Single'}
            </span>
          </div>
        </div>
      </div>

      {/* Seamless Writing Interface */}
      <div style={{
        width: '100%',
        maxWidth: '900px',
        background: 'transparent',
        position: 'relative',
        minHeight: '500px',
        display: 'flex',
        flexDirection: 'column'
      }}>

        {/* Clean Writing Area */}
        <div style={{
          flex: 1,
          position: 'relative',
          zIndex: 1
        }}>
          <textarea
            ref={textareaRef}
            value={content}
            onChange={(e) => setContent(e.target.value)}
            onKeyDown={handleTabPress}
            placeholder={aiEnabled ? 'Start writing and I\'ll help you continue...' : 'What\'s on your mind?'}
            style={{
              width: '100%',
              height: '100%',
              minHeight: '400px',
              padding: '0',
              border: 'none',
              background: 'transparent',
              fontSize: '18px',
              lineHeight: '1.8',
              color: colors.text.primary,
              fontFamily: 'Georgia, serif',
              resize: 'none',
              outline: 'none',
              letterSpacing: '0.2px'
            }}
          />

          {/* Contextual AI Suggestion */}
          {showSuggestion && aiEnabled && (
            <div style={{
              position: 'absolute',
              bottom: '20px',
              left: '0',
              right: '0',
              padding: '16px 20px',
              background: `linear-gradient(135deg, ${colors.primary} 0%, ${colors.primaryLight} 100%)`,
              borderRadius: '12px',
              color: 'white',
              fontSize: '16px',
              boxShadow: `0 8px 24px ${colors.primary}30`,
              display: 'flex',
              alignItems: 'center',
              gap: '12px',
              backdropFilter: 'blur(10px)'
            }}>
              <Sparkles size={18} color="white" />
              <span style={{ flex: 1, lineHeight: '1.4' }}>
                {aiSuggestion}
              </span>
              <span style={{
                padding: '4px 8px',
                background: 'rgba(255, 255, 255, 0.25)',
                borderRadius: '6px',
                fontSize: '12px',
                fontWeight: '600',
                letterSpacing: '0.5px'
              }}>
                TAB
              </span>
            </div>
          )}
        </div>

        {/* Minimal Action Bar */}
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginTop: '40px',
          paddingTop: '20px',
          borderTop: `1px solid ${colors.border}`,
          position: 'relative',
          zIndex: 1
        }}>
          <div style={{
            color: colors.text.tertiary,
            fontSize: '14px',
            fontWeight: '400'
          }}>
            {content.length} characters
            {content.length > 280 && (
              <span style={{
                color: colors.primary,
                marginLeft: '8px',
                fontSize: '12px'
              }}>
                • Thread mode recommended
              </span>
            )}
          </div>

          <div style={{ display: 'flex', gap: '12px' }}>
            <button style={{
              padding: '10px 20px',
              background: colors.surface,
              color: colors.text.primary,
              border: `1px solid ${colors.border}`,
              borderRadius: '8px',
              fontSize: '14px',
              fontWeight: '500',
              cursor: 'pointer',
              transition: 'all 0.2s ease'
            }}>
              Save Draft
            </button>

            <button style={{
              padding: '10px 24px',
              background: `linear-gradient(135deg, ${colors.primary} 0%, ${colors.primaryLight} 100%)`,
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              fontSize: '14px',
              fontWeight: '600',
              cursor: 'pointer',
              transition: 'all 0.2s ease',
              boxShadow: `0 4px 12px ${colors.primary}30`
            }}>
              Publish
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

TweetCenterPage.getLayout = function getLayout(page: ReactElement) {
  return (
    <SidebarLayout>
      {page}
    </SidebarLayout>
  );
};

export default TweetCenterPage;