// pages/settings.tsx
import React, { useState } from 'react';
import SidebarLayout from '../components/SidebarLayout';
import { Save, Plus, Trash2, <PERSON><PERSON>, <PERSON>, Twitter } from 'lucide-react';
import type { ReactElement } from 'react';
import type { NextPageWithLayout } from './_app';

const SettingsPage: NextPageWithLayout = () => {
  const [customPrompts, setCustomPrompts] = useState([
    {
      id: 1,
      name: 'Professional Tweet',
      prompt: 'Write a professional, engaging tweet about {topic} that provides value to entrepreneurs and business leaders. Keep it under 280 characters and include relevant hashtags.'
    },
    {
      id: 2,
      name: 'Thread Starter',
      prompt: 'Create an engaging thread starter about {topic} that hooks the reader and promises valuable insights. End with "🧵 Thread below:"'
    }
  ]);

  const [newPrompt, setNewPrompt] = useState({ name: '', prompt: '' });
  const [agentESettings, setAgentESettings] = useState({
    enabled: false,
    autoPost: false,
    scheduleTime: '09:00',
    dailyLimit: 3,
    contentStyle: 'professional'
  });

  const colors = {
    primary: '#FF6B35',
    primaryLight: '#FF8A65',
    text: {
      primary: '#2D1B14',
      secondary: '#5D4037',
      tertiary: '#8D6E63'
    },
    border: '#F5E6D3',
    surface: '#FFFFFF',
    warmGlow: '#FFE0B2',
    background: '#FFF8F0'
  };

  const addCustomPrompt = () => {
    if (newPrompt.name && newPrompt.prompt) {
      setCustomPrompts([...customPrompts, {
        id: Date.now(),
        name: newPrompt.name,
        prompt: newPrompt.prompt
      }]);
      setNewPrompt({ name: '', prompt: '' });
    }
  };

  const deletePrompt = (id: number) => {
    setCustomPrompts(customPrompts.filter(p => p.id !== id));
  };

  return (
    <div style={{
      minHeight: '100vh',
      background: `linear-gradient(135deg, ${colors.background} 0%, ${colors.warmGlow} 100%)`,
      padding: '40px 60px'
    }}>
      <div style={{
        maxWidth: '1000px',
        margin: '0 auto'
      }}>
        {/* Header */}
        <div style={{ marginBottom: '40px' }}>
          <h1 className="playfair" style={{
            fontSize: '42px',
            fontWeight: '400',
            color: colors.text.primary,
            marginBottom: '8px',
            letterSpacing: '-0.5px'
          }}>
            Settings
          </h1>
          <p className="sf-pro" style={{
            fontSize: '16px',
            color: colors.text.secondary,
            fontWeight: '400'
          }}>
            Customize your AI writing experience and Agent E automation
          </p>
        </div>

        {/* Agent E Configuration */}
        <div style={{
          background: colors.surface,
          borderRadius: '16px',
          padding: '32px',
          marginBottom: '32px',
          boxShadow: '0 4px 16px rgba(0, 0, 0, 0.06)',
          border: `1px solid ${colors.border}`
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '24px' }}>
            <Bot size={24} color={colors.primary} />
            <h2 className="playfair" style={{
              fontSize: '28px',
              fontWeight: '400',
              color: colors.text.primary,
              margin: 0
            }}>
              Agent E
            </h2>
          </div>

          <p className="sf-pro" style={{
            fontSize: '14px',
            color: colors.text.secondary,
            marginBottom: '24px',
            lineHeight: '1.6'
          }}>
            Your AI assistant that automatically creates, schedules, and posts content to your X account
          </p>

          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '24px' }}>
            <div>
              <label className="sf-pro" style={{
                display: 'block',
                fontSize: '14px',
                fontWeight: '600',
                color: colors.text.primary,
                marginBottom: '8px'
              }}>
                Enable Agent E
              </label>
              <label style={{ display: 'flex', alignItems: 'center', gap: '8px', cursor: 'pointer' }}>
                <input
                  type="checkbox"
                  checked={agentESettings.enabled}
                  onChange={(e) => setAgentESettings({...agentESettings, enabled: e.target.checked})}
                  style={{ accentColor: colors.primary }}
                />
                <span className="sf-pro" style={{ fontSize: '14px', color: colors.text.secondary }}>
                  Activate autonomous posting
                </span>
              </label>
            </div>

            <div>
              <label className="sf-pro" style={{
                display: 'block',
                fontSize: '14px',
                fontWeight: '600',
                color: colors.text.primary,
                marginBottom: '8px'
              }}>
                Daily Post Limit
              </label>
              <input
                type="number"
                min="1"
                max="10"
                value={agentESettings.dailyLimit}
                onChange={(e) => setAgentESettings({...agentESettings, dailyLimit: parseInt(e.target.value)})}
                className="sf-pro"
                style={{
                  width: '100%',
                  padding: '12px',
                  border: `1px solid ${colors.border}`,
                  borderRadius: '8px',
                  fontSize: '14px',
                  background: colors.surface
                }}
              />
            </div>

            <div>
              <label className="sf-pro" style={{
                display: 'block',
                fontSize: '14px',
                fontWeight: '600',
                color: colors.text.primary,
                marginBottom: '8px'
              }}>
                Preferred Posting Time
              </label>
              <input
                type="time"
                value={agentESettings.scheduleTime}
                onChange={(e) => setAgentESettings({...agentESettings, scheduleTime: e.target.value})}
                className="sf-pro"
                style={{
                  width: '100%',
                  padding: '12px',
                  border: `1px solid ${colors.border}`,
                  borderRadius: '8px',
                  fontSize: '14px',
                  background: colors.surface
                }}
              />
            </div>
          </div>
        </div>

        {/* Custom Prompts */}
        <div style={{
          background: colors.surface,
          borderRadius: '16px',
          padding: '32px',
          boxShadow: '0 4px 16px rgba(0, 0, 0, 0.06)',
          border: `1px solid ${colors.border}`
        }}>
          <h2 className="playfair" style={{
            fontSize: '28px',
            fontWeight: '400',
            color: colors.text.primary,
            marginBottom: '24px'
          }}>
            Custom Prompts
          </h2>

          {/* Existing Prompts */}
          <div style={{ marginBottom: '32px' }}>
            {customPrompts.map((prompt) => (
              <div key={prompt.id} style={{
                padding: '20px',
                border: `1px solid ${colors.border}`,
                borderRadius: '12px',
                marginBottom: '16px',
                background: colors.warmGlow + '20'
              }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '12px' }}>
                  <h3 className="sf-pro" style={{
                    fontSize: '16px',
                    fontWeight: '600',
                    color: colors.text.primary,
                    margin: 0
                  }}>
                    {prompt.name}
                  </h3>
                  <button
                    onClick={() => deletePrompt(prompt.id)}
                    style={{
                      background: 'none',
                      border: 'none',
                      cursor: 'pointer',
                      color: colors.text.tertiary,
                      padding: '4px'
                    }}
                  >
                    <Trash2 size={16} />
                  </button>
                </div>
                <p className="sf-pro" style={{
                  fontSize: '14px',
                  color: colors.text.secondary,
                  lineHeight: '1.5',
                  margin: 0
                }}>
                  {prompt.prompt}
                </p>
              </div>
            ))}
          </div>

          {/* Add New Prompt */}
          <div style={{
            padding: '24px',
            border: `2px dashed ${colors.border}`,
            borderRadius: '12px',
            background: colors.warmGlow + '10'
          }}>
            <h3 className="sf-pro" style={{
              fontSize: '16px',
              fontWeight: '600',
              color: colors.text.primary,
              marginBottom: '16px'
            }}>
              Add New Prompt
            </h3>
            
            <div style={{ marginBottom: '16px' }}>
              <input
                type="text"
                placeholder="Prompt name (e.g., 'Motivational Quote')"
                value={newPrompt.name}
                onChange={(e) => setNewPrompt({...newPrompt, name: e.target.value})}
                className="sf-pro"
                style={{
                  width: '100%',
                  padding: '12px',
                  border: `1px solid ${colors.border}`,
                  borderRadius: '8px',
                  fontSize: '14px',
                  background: colors.surface
                }}
              />
            </div>
            
            <div style={{ marginBottom: '16px' }}>
              <textarea
                placeholder="Prompt template (use {topic} for dynamic content)"
                value={newPrompt.prompt}
                onChange={(e) => setNewPrompt({...newPrompt, prompt: e.target.value})}
                className="sf-pro"
                rows={3}
                style={{
                  width: '100%',
                  padding: '12px',
                  border: `1px solid ${colors.border}`,
                  borderRadius: '8px',
                  fontSize: '14px',
                  background: colors.surface,
                  resize: 'vertical'
                }}
              />
            </div>
            
            <button
              onClick={addCustomPrompt}
              className="sf-pro"
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                padding: '12px 20px',
                background: `linear-gradient(135deg, ${colors.primary} 0%, ${colors.primaryLight} 100%)`,
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                fontSize: '14px',
                fontWeight: '600',
                cursor: 'pointer',
                transition: 'all 0.2s ease'
              }}
            >
              <Plus size={16} />
              Add Prompt
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

SettingsPage.getLayout = function getLayout(page: ReactElement) {
  return (
    <SidebarLayout>
      {page}
    </SidebarLayout>
  );
};

export default SettingsPage;
