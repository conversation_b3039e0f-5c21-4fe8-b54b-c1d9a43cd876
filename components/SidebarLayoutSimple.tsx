import Link from 'next/link';
import React from 'react';
import { useRouter } from 'next/router';
import { Home, BarChart3, MessageCircle, Video, User } from 'lucide-react';

interface SidebarLayoutProps {
  children: React.ReactNode;
}

const SidebarLayout: React.FC<SidebarLayoutProps> = ({ children }) => {
  const router = useRouter();

  const colors = {
    primary: '#FF6B35',
    primaryLight: '#FF8A65',
    background: '#F5F1EB', // Chill beige background
    surface: '#FFFFFF',
    text: {
      primary: '#2D1B14',
      secondary: '#5D4037'
    },
    sidebar: {
      background: 'linear-gradient(135deg, #FF6B35 0%, #FF8A65 100%)',
      text: '#FFFFFF'
    }
  };

  const menuItems = [
    { href: '/', label: 'Briefing Room', icon: Home },
    { href: '/tweet-center', label: 'Drafting Desk', icon: MessageCircle },
    { href: '/dashboard', label: 'Growth Lab', icon: BarChart3 },
    { href: '/meeting', label: 'AI Meetings', icon: Video }
  ];

  const isActive = (href: string) => {
    if (href === '/') {
      return router.pathname === '/';
    }
    return router.pathname.startsWith(href);
  };

  return (
    <div style={{
      display: 'flex',
      minHeight: '100vh',
      background: colors.background,
      padding: '20px',
      gap: '20px'
    }}>
      <aside style={{
        width: '160px', // More compact width
        background: colors.sidebar.background,
        minHeight: 'calc(100vh - 40px)',
        borderRadius: '12px', // Slightly smaller radius
        display: 'flex',
        flexDirection: 'column',
        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)' // Subtle shadow
      }}>
        <div style={{
          padding: '16px',
          textAlign: 'center'
        }}>
          <span style={{
            fontSize: '28px',
            color: colors.sidebar.text,
            fontFamily: 'Georgia, serif',
            fontStyle: 'italic'
          }}>
            ℰ
          </span>
        </div>

        <nav style={{ flex: 1, padding: '12px 12px' }}>
          {menuItems.map((item) => {
            const active = isActive(item.href);
            return (
              <div key={item.href} style={{ marginBottom: '4px' }}>
                <Link href={item.href} style={{ textDecoration: 'none' }}>
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    padding: '10px 12px',
                    borderRadius: '8px',
                    background: active ? 'rgba(255, 255, 255, 0.2)' : 'transparent',
                    cursor: 'pointer',
                    transition: 'all 0.2s ease'
                  }}
                  onMouseEnter={(e) => {
                    if (!active) {
                      e.currentTarget.style.background = 'rgba(255, 255, 255, 0.1)';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (!active) {
                      e.currentTarget.style.background = 'transparent';
                    }
                  }}
                  >
                    <item.icon size={14} color={colors.sidebar.text} />
                    <span style={{
                      marginLeft: '10px',
                      color: colors.sidebar.text,
                      fontSize: '13px',
                      fontWeight: active ? '600' : '500'
                    }}>
                      {item.label}
                    </span>
                  </div>
                </Link>
              </div>
            );
          })}
        </nav>

        {/* Account Section */}
        <div style={{
          padding: '12px',
          borderTop: '1px solid rgba(255, 255, 255, 0.2)'
        }}>
          <Link href="/settings" style={{ textDecoration: 'none' }}>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              gap: '8px',
              padding: '8px 12px',
              background: 'rgba(255, 255, 255, 0.1)',
              borderRadius: '8px',
              cursor: 'pointer',
              transition: 'all 0.2s ease',
              border: '1px solid rgba(255, 255, 255, 0.1)'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.background = 'rgba(255, 255, 255, 0.2)';
              e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.3)';
              e.currentTarget.style.transform = 'translateY(-1px)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.background = 'rgba(255, 255, 255, 0.1)';
              e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.1)';
              e.currentTarget.style.transform = 'translateY(0)';
            }}
            >
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '8px'
              }}>
                <div style={{
                  width: '24px',
                  height: '24px',
                  background: 'rgba(255, 255, 255, 0.2)',
                  borderRadius: '6px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <User size={12} color={colors.sidebar.text} />
                </div>
                <div>
                  <div style={{
                    color: colors.sidebar.text,
                    fontSize: '12px',
                    fontWeight: '600'
                  }}>
                    Alex Chen
                  </div>
                  <div style={{
                    color: 'rgba(255, 255, 255, 0.7)',
                    fontSize: '10px',
                    fontWeight: '400'
                  }}>
                    Settings
                  </div>
                </div>
              </div>
              <div style={{
                color: 'rgba(255, 255, 255, 0.6)',
                fontSize: '10px'
              }}>
                →
              </div>
            </div>
          </Link>
        </div>
      </aside>

      <main style={{
        flexGrow: 1,
        backgroundColor: colors.surface,
        borderRadius: '20px',
        minHeight: 'calc(100vh - 40px)',
        position: 'relative'
      }}>
        <div style={{
          padding: '40px',
          height: '100%'
        }}>
          {children}
        </div>
      </main>
    </div>
  );
};

export default SidebarLayout;
