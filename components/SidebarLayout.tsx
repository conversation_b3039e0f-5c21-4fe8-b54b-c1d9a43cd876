import Link from 'next/link';
import React, { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/router';
import { Home, BarChart3, MessageCircle, Video, Settings, CreditCard, DollarSign, HelpCircle, LogOut, User, Crown } from 'lucide-react';

interface SidebarLayoutProps {
  children: React.ReactNode;
}

const SidebarLayout: React.FC<SidebarLayoutProps> = ({ children }) => {
  const router = useRouter();
  const [hoveredItem, setHoveredItem] = useState<string | null>(null);
  const [showAccountMenu, setShowAccountMenu] = useState(false);
  const accountMenuRef = useRef<HTMLDivElement>(null);

  // Warm Intelligence Theme - Focused, Intelligent, Warm
  const colors = {
    primary: '#FF6B35',        // Warm coral-orange
    primaryLight: '#FF8A65',   // Soft warm orange
    primaryDark: '#E65100',    // Deep warm orange
    accent: '#FFF3E0',         // Warm cream
    surface: '#FEFEFE',        // Warm white
    surfaceElevated: '#FFFFFF', // Pure white
    background: '#FFF8F3',     // Warm background
    warmGlow: '#FFE0B2',       // Subtle warm glow
    text: {
      primary: '#2D1B14',      // Warm dark brown
      secondary: '#5D4037',    // Medium warm brown
      tertiary: '#8D6E63',     // Light warm brown
      muted: '#BCAAA4',        // Warm gray
      inverse: '#FFFFFF'       // White text
    },
    border: {
      light: '#F5E6D3',        // Warm light border
      medium: '#E1C4A0',       // Warm medium border
      primary: '#FF6B35'       // Primary border
    },
    sidebar: {
      background: 'linear-gradient(135deg, #FF6B35 0%, #FF8A65 50%, #FFB74D 100%)',
      backgroundSolid: '#FF6B35',
      text: '#FFFFFF',
      textSecondary: 'rgba(255, 255, 255, 0.9)',
      textTertiary: 'rgba(255, 255, 255, 0.7)',
      hover: 'rgba(255, 255, 255, 0.15)',
      active: 'rgba(255, 255, 255, 0.25)',
      border: 'rgba(255, 255, 255, 0.2)',
      glow: 'rgba(255, 107, 53, 0.3)'
    }
  };

  const menuItems = [
    {
      href: '/',
      label: 'Briefing Room',
      icon: Home,
      description: 'Mission Control'
    },
    {
      href: '/tweet-center',
      label: 'Drafting Desk',
      icon: MessageCircle,
      description: 'AI Writing'
    },
    {
      href: '/dashboard',
      label: 'Growth Lab',
      icon: BarChart3,
      description: 'Analytics'
    },
    {
      href: '/meeting',
      label: 'AI Meetings',
      icon: Video,
      description: 'Video Calls'
    }
  ];

  const isActive = (href: string) => {
    if (href === '/') {
      return router.pathname === '/';
    }
    return router.pathname.startsWith(href);
  };

  return (
    <div style={{
      display: 'flex',
      minHeight: '100vh',
      background: `
        radial-gradient(circle at 20% 20%, ${colors.primary}15 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, ${colors.primaryLight}10 0%, transparent 50%),
        linear-gradient(135deg, ${colors.primary} 0%, ${colors.primaryLight} 100%)
      `,
      padding: '20px',
      gap: '20px'
    }}>
      {/* Clean Warm Sidebar */}
      <aside style={{
        width: '200px',
        background: colors.sidebar.background,
        minHeight: 'calc(100vh - 40px)',
        position: 'relative',
        display: 'flex',
        flexDirection: 'column',
        borderRadius: '16px',
        boxShadow: `
          0 20px 60px ${colors.sidebar.glow},
          0 8px 32px rgba(0, 0, 0, 0.15),
          inset 0 1px 0 rgba(255, 255, 255, 0.2)
        `,
        overflow: 'hidden',
        backdropFilter: 'blur(10px)',
        border: `1px solid rgba(255, 255, 255, 0.1)`
      }}>
        {/* Clean Brand Header */}
        <div style={{
          padding: '24px',
          borderBottom: `1px solid ${colors.sidebar.border}`,
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center'
        }}>
          {/* Clean elegant cursive E */}
          <span style={{
            fontSize: '32px',
            color: colors.sidebar.text,
            fontWeight: '400',
            fontFamily: 'Georgia, serif',
            fontStyle: 'italic',
            textShadow: '0 2px 8px rgba(0, 0, 0, 0.3)',
            letterSpacing: '-1px'
          }}>
            ℰ
          </span>
        </div>

        {/* Clean Navigation */}
        <nav style={{ flex: 1, padding: '20px 0', overflow: 'auto' }}>
          <div style={{ padding: '0 16px' }}>
            {menuItems.map((item, index) => {
              const active = isActive(item.href);
              const hovered = hoveredItem === item.href;

              return (
                <div key={item.href} style={{ marginBottom: '6px' }}>
                  <Link
                    href={item.href}
                    style={{ textDecoration: 'none' }}
                    onMouseEnter={() => setHoveredItem(item.href)}
                    onMouseLeave={() => setHoveredItem(null)}
                  >
                    <div style={{
                      display: 'flex',
                      alignItems: 'center',
                      padding: '12px 16px',
                      borderRadius: '12px',
                      transition: 'all 0.2s ease',
                      cursor: 'pointer',
                      position: 'relative',
                      background: active
                        ? `linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.1) 100%)`
                        : hovered
                          ? `linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 100%)`
                          : 'transparent',
                      backdropFilter: (active || hovered) ? 'blur(10px)' : 'none',
                      border: active
                        ? '1px solid rgba(255, 255, 255, 0.3)'
                        : '1px solid transparent',
                      boxShadow: active
                        ? `0 4px 16px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.4)`
                        : 'none'
                    }}>
                      {/* Active indicator */}
                      {active && (
                        <div style={{
                          position: 'absolute',
                          left: '-1px',
                          top: '50%',
                          transform: 'translateY(-50%)',
                          width: '3px',
                          height: '20px',
                          background: `linear-gradient(180deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.6) 100%)`,
                          borderRadius: '0 6px 6px 0',
                          boxShadow: '0 0 8px rgba(255, 255, 255, 0.5)'
                        }} />
                      )}

                      {/* Icon container */}
                      <div style={{
                        width: '24px',
                        height: '24px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        marginRight: '12px',
                        borderRadius: '6px',
                        background: active
                          ? `linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.1) 100%)`
                          : 'transparent'
                      }}>
                        <item.icon
                          size={16}
                          color={colors.sidebar.text}
                          style={{
                            filter: active ? 'drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2))' : 'none'
                          }}
                        />
                      </div>

                      {/* Label */}
                      <div style={{
                        fontSize: '15px',
                        fontWeight: active ? '600' : '500',
                        color: colors.sidebar.text,
                        letterSpacing: '-0.3px',
                        textShadow: active ? '0 1px 2px rgba(0, 0, 0, 0.1)' : 'none'
                      }}>
                        {item.label}
                      </div>
                    </div>
                  </Link>
                </div>
              );
            })}
          </div>
        </nav>

        {/* Compact Account Section */}
        <div
          ref={accountMenuRef}
          style={{
            padding: '16px 12px',
            borderTop: `1px solid ${colors.sidebar.border}`,
            marginTop: 'auto',
            background: `radial-gradient(circle at center bottom, rgba(255, 255, 255, 0.1) 0%, transparent 70%)`,
            position: 'relative'
          }}
          onMouseEnter={() => setShowAccountMenu(true)}
          onMouseLeave={() => setShowAccountMenu(false)}
        >
          {/* Compact Account Profile */}
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              padding: '8px 12px',
              background: `linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.05) 100%)`,
              borderRadius: '8px',
              cursor: 'pointer',
              transition: 'all 0.15s ease',
              backdropFilter: 'blur(10px)',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              boxShadow: `0 2px 8px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.3)`
            }}
          >
            {/* Compact Avatar */}
            <div style={{
              width: '24px',
              height: '24px',
              background: `linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.1) 100%)`,
              borderRadius: '6px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              position: 'relative',
              backdropFilter: 'blur(10px)',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              flexShrink: 0
            }}>
              <span style={{
                color: colors.sidebar.text,
                fontSize: '11px',
                fontWeight: '600',
                textShadow: '0 1px 2px rgba(0, 0, 0, 0.1)'
              }}>
                A
              </span>
              {/* Online indicator */}
              <div style={{
                position: 'absolute',
                bottom: '-1px',
                right: '-1px',
                width: '6px',
                height: '6px',
                borderRadius: '50%',
                background: `radial-gradient(circle, #00E676 0%, #00C853 100%)`,
                border: '1px solid rgba(255, 255, 255, 0.9)'
              }} />
            </div>

            {/* Compact User Info */}
            <div style={{ flex: 1, minWidth: 0 }}>
              <div style={{
                fontSize: '12px',
                fontWeight: '600',
                color: colors.sidebar.text,
                lineHeight: '1.2',
                marginBottom: '1px',
                textShadow: '0 1px 2px rgba(0, 0, 0, 0.1)',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap'
              }}>
                Alex Chen
              </div>
              <div style={{
                fontSize: '10px',
                color: colors.sidebar.textTertiary,
                lineHeight: '1.2',
                fontWeight: '500'
              }}>
                AI Manager
              </div>
            </div>

            {/* Dropdown indicator */}
            <div style={{
              width: '12px',
              height: '12px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              borderRadius: '3px',
              background: `linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.05) 100%)`,
              flexShrink: 0
            }}>
              <span style={{
                fontSize: '8px',
                color: colors.sidebar.textSecondary,
                transform: showAccountMenu ? 'rotate(180deg)' : 'rotate(0deg)',
                transition: 'transform 0.2s ease'
              }}>
                ⌄
              </span>
            </div>
          </div>

          {/* Horizontal Account Menu Popup */}
          {showAccountMenu && (
            <div style={{
              position: 'absolute',
              left: '100%',
              bottom: '0',
              marginLeft: '12px',
              width: '320px',
              background: `
                linear-gradient(135deg,
                  rgba(255, 255, 255, 0.98) 0%,
                  rgba(255, 255, 255, 0.95) 100%
                )
              `,
              borderRadius: '12px',
              boxShadow: `
                0 8px 32px rgba(0, 0, 0, 0.12),
                0 4px 16px rgba(0, 0, 0, 0.08),
                0 2px 8px rgba(255, 107, 53, 0.15),
                inset 0 1px 0 rgba(255, 255, 255, 0.9)
              `,
              border: `1px solid rgba(255, 107, 53, 0.15)`,
              overflow: 'hidden',
              zIndex: 1000,
              backdropFilter: 'blur(16px)',
              opacity: showAccountMenu ? 1 : 0,
              transform: showAccountMenu ? 'translateX(0)' : 'translateX(-8px)',
              transition: 'all 0.15s ease'
            }}>
              {/* Compact Header */}
              <div style={{
                padding: '12px 16px',
                borderBottom: `1px solid rgba(255, 107, 53, 0.1)`,
                background: `linear-gradient(135deg, rgba(255, 107, 53, 0.08) 0%, rgba(255, 138, 101, 0.04) 100%)`
              }}>
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px'
                }}>
                  <div style={{
                    width: '28px',
                    height: '28px',
                    background: `linear-gradient(135deg, ${colors.primary} 0%, ${colors.primaryLight} 100%)`,
                    borderRadius: '6px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    boxShadow: '0 2px 8px rgba(255, 107, 53, 0.25)',
                    position: 'relative'
                  }}>
                    <span style={{
                      color: 'white',
                      fontSize: '12px',
                      fontWeight: '600',
                      textShadow: '0 1px 2px rgba(0, 0, 0, 0.2)'
                    }}>
                      A
                    </span>
                    <div style={{
                      position: 'absolute',
                      bottom: '-1px',
                      right: '-1px',
                      width: '8px',
                      height: '8px',
                      borderRadius: '50%',
                      background: `linear-gradient(135deg, #00E676 0%, #00C853 100%)`,
                      border: '1.5px solid white',
                      boxShadow: '0 0 4px rgba(0, 230, 118, 0.4)'
                    }} />
                  </div>
                  <div>
                    <div style={{
                      fontSize: '13px',
                      fontWeight: '600',
                      color: colors.text.primary,
                      lineHeight: '1.2'
                    }}>
                      Alex Chen
                    </div>
                    <div style={{
                      fontSize: '11px',
                      color: colors.text.secondary,
                      fontWeight: '500'
                    }}>
                      Pro Member
                    </div>
                  </div>
                </div>
              </div>

              {/* Horizontal Menu Items */}
              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(2, 1fr)',
                gap: '1px',
                background: `rgba(255, 107, 53, 0.06)`
              }}>
                {[
                  { label: 'Settings', icon: Settings },
                  { label: 'Subscription', icon: Crown },
                  { label: 'Billing', icon: CreditCard },
                  { label: 'Help', icon: HelpCircle }
                ].map((item, index) => (
                  <div
                    key={index}
                    style={{
                      padding: '12px',
                      cursor: 'pointer',
                      transition: 'all 0.1s ease',
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      gap: '6px',
                      background: 'rgba(255, 255, 255, 0.8)',
                      position: 'relative'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.background = `rgba(255, 107, 53, 0.08)`;
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.background = 'rgba(255, 255, 255, 0.8)';
                    }}
                  >
                    <div style={{
                      width: '28px',
                      height: '28px',
                      borderRadius: '6px',
                      background: `linear-gradient(135deg, rgba(255, 107, 53, 0.12) 0%, rgba(255, 138, 101, 0.06) 100%)`,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      border: `1px solid rgba(255, 107, 53, 0.15)`
                    }}>
                      <item.icon
                        size={14}
                        color={colors.primary}
                        style={{
                          filter: 'drop-shadow(0 1px 2px rgba(255, 107, 53, 0.2))'
                        }}
                      />
                    </div>
                    <div style={{
                      fontSize: '11px',
                      fontWeight: '500',
                      color: colors.text.primary,
                      textAlign: 'center'
                    }}>
                      {item.label}
                    </div>
                  </div>
                ))}
              </div>

              {/* Sign Out Section */}
              <div style={{
                padding: '8px 12px',
                borderTop: `1px solid rgba(255, 107, 53, 0.1)`
              }}>
                <div
                  style={{
                    padding: '8px 12px',
                    cursor: 'pointer',
                    transition: 'all 0.1s ease',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px',
                    borderRadius: '6px',
                    background: 'transparent'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.background = `rgba(231, 76, 60, 0.08)`;
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.background = 'transparent';
                  }}
                >
                  <div style={{
                    width: '20px',
                    height: '20px',
                    borderRadius: '4px',
                    background: `linear-gradient(135deg, rgba(231, 76, 60, 0.12) 0%, rgba(231, 76, 60, 0.06) 100%)`,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    border: `1px solid rgba(231, 76, 60, 0.2)`
                  }}>
                    <LogOut
                      size={11}
                      color="#e74c3c"
                    />
                  </div>
                  <div style={{
                    fontSize: '12px',
                    fontWeight: '500',
                    color: '#e74c3c'
                  }}>
                    Sign Out
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </aside>

      {/* Intelligent Content Canvas */}
      <main style={{
        flexGrow: 1,
        position: 'relative'
      }}>
        <div style={{
          backgroundColor: colors.surfaceElevated,
          borderRadius: '20px',
          minHeight: 'calc(100vh - 40px)',
          boxShadow: `
            0 32px 80px rgba(0, 0, 0, 0.12),
            0 8px 32px rgba(0, 0, 0, 0.08),
            inset 0 1px 0 rgba(255, 255, 255, 0.9),
            0 0 0 1px rgba(255, 107, 53, 0.1)
          `,
          overflow: 'hidden',
          position: 'relative',
          backdropFilter: 'blur(20px)',
          border: '1px solid rgba(255, 255, 255, 0.2)'
        }}>
          {/* Subtle warm ambient lighting */}
          <div style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            height: '200px',
            background: `
              radial-gradient(ellipse at top, ${colors.warmGlow}20 0%, transparent 70%)
            `,
            pointerEvents: 'none'
          }} />

          {/* Content with warm context */}
          <div style={{
            position: 'relative',
            zIndex: 1,
            height: '100%'
          }}>
            {children}
          </div>
        </div>
      </main>
    </div>
  );
};

export default SidebarLayout;