// components/AgentEChat.tsx
import React, { useState, useRef, useEffect } from 'react';
import { Bo<PERSON>, Send, Loader, CheckCircle, Clock, Twitter, Sparkles } from 'lucide-react';

interface Message {
  id: string;
  type: 'user' | 'agent';
  content: string;
  timestamp: Date;
  status?: 'sending' | 'sent' | 'scheduled' | 'posted';
  tweetData?: {
    text: string;
    scheduledFor?: Date;
    posted?: boolean;
  };
}

interface AgentEChatProps {
  isOpen: boolean;
  onClose: () => void;
  currentContent: string;
}

const AgentEChat: React.FC<AgentEChatProps> = ({ isOpen, onClose, currentContent }) => {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      type: 'agent',
      content: "Hi! I'm Agent E, your AI posting assistant. I can help you create, schedule, and automatically post content to your X account. What would you like me to help you with today?",
      timestamp: new Date()
    }
  ]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const colors = {
    primary: '#FF6B35',
    primaryLight: '#FF8A65',
    text: {
      primary: '#2D1B14',
      secondary: '#5D4037',
      tertiary: '#8D6E63'
    },
    border: '#F5E6D3',
    surface: '#FFFFFF',
    warmGlow: '#FFE0B2',
    background: '#FFF8F0'
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const callAgentAPI = async (action: string, content: string): Promise<any> => {
    try {
      const response = await fetch('/api/agent-e/post', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content,
          action,
          scheduleTime: action === 'schedule' ? '09:00' : undefined
        }),
      });

      return await response.json();
    } catch (error) {
      console.error('API Error:', error);
      return { success: false, message: 'Failed to connect to Agent E' };
    }
  };

  const simulateAgentResponse = async (userMessage: string): Promise<string> => {
    const lowerMessage = userMessage.toLowerCase();

    if (lowerMessage.includes('schedule') || lowerMessage.includes('post later')) {
      const result = await callAgentAPI('schedule', currentContent);
      return result.success
        ? `✅ Scheduled your post for ${result.scheduledFor}! I'll automatically post it at the optimal time for maximum engagement.`
        : "I'll help you schedule this post! When would you like it to go live?";
    }

    if (lowerMessage.includes('improve') || lowerMessage.includes('better')) {
      const result = await callAgentAPI('improve', currentContent);
      return result.success
        ? `✨ Here's an improved version:\n\n"${result.improvedContent}"\n\nI've optimized it for better engagement and added relevant elements!`
        : "Let me enhance your content for better engagement. I'll optimize it for X's algorithm and ensure it resonates with your audience.";
    }

    if (lowerMessage.includes('thread') || lowerMessage.includes('multiple tweets')) {
      const result = await callAgentAPI('thread', currentContent);
      return result.success
        ? `🧵 I've created a ${result.threadContent.length}-part thread:\n\n${result.threadContent.map((tweet: string, i: number) => `${i + 1}. ${tweet}`).join('\n\n')}`
        : "Great idea! I'll break this into an engaging thread for better storytelling.";
    }

    if (lowerMessage.includes('post now') || lowerMessage.includes('publish')) {
      const result = await callAgentAPI('post', currentContent);
      return result.success
        ? `🚀 Successfully posted to your X account! Tweet ID: ${result.tweetId}\n\nYour content is now live and engaging with your audience.`
        : `❌ ${result.message || 'Failed to post. Please check your content and try again.'}`;
    }

    return "I understand! Let me help you create compelling content that will resonate with your audience. I can:\n\n• Post immediately\n• Schedule for later\n• Improve your content\n• Create a thread\n\nWhat would you like to do?";
  };

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: inputValue,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsLoading(true);

    try {
      const agentResponse = await simulateAgentResponse(inputValue);

      const agentMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'agent',
        content: agentResponse,
        timestamp: new Date()
      };

      setMessages(prev => [...prev, agentMessage]);

      // Simulate posting action if user requested it
      if (inputValue.toLowerCase().includes('post now')) {
        setTimeout(() => {
          const postConfirmation: Message = {
            id: (Date.now() + 2).toString(),
            type: 'agent',
            content: "✅ Successfully posted to your X account! Your content is now live and engaging with your audience.",
            timestamp: new Date(),
            status: 'posted',
            tweetData: {
              text: currentContent || "Sample tweet content",
              posted: true
            }
          };
          setMessages(prev => [...prev, postConfirmation]);
        }, 2000);
      }
    } catch (error) {
      console.error('Error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  if (!isOpen) return null;

  return (
    <div style={{
      position: 'fixed',
      bottom: '20px',
      right: '20px',
      width: '400px',
      height: '600px',
      background: colors.surface,
      borderRadius: '16px',
      boxShadow: '0 12px 48px rgba(0, 0, 0, 0.15)',
      border: `1px solid ${colors.border}`,
      display: 'flex',
      flexDirection: 'column',
      zIndex: 1000,
      overflow: 'hidden'
    }}>
      {/* Header */}
      <div style={{
        padding: '20px',
        borderBottom: `1px solid ${colors.border}`,
        background: `linear-gradient(135deg, ${colors.primary} 0%, ${colors.primaryLight} 100%)`,
        color: 'white'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
            <div style={{
              width: '32px',
              height: '32px',
              background: 'rgba(255, 255, 255, 0.2)',
              borderRadius: '8px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <Bot size={18} color="white" />
            </div>
            <div>
              <h3 className="sf-pro" style={{
                fontSize: '16px',
                fontWeight: '600',
                margin: 0,
                color: 'white'
              }}>
                Agent E
              </h3>
              <p className="sf-pro" style={{
                fontSize: '12px',
                margin: 0,
                opacity: 0.8,
                color: 'white'
              }}>
                AI Posting Assistant
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            style={{
              background: 'none',
              border: 'none',
              color: 'white',
              cursor: 'pointer',
              fontSize: '18px',
              opacity: 0.8,
              padding: '4px'
            }}
          >
            ×
          </button>
        </div>
      </div>

      {/* Messages */}
      <div style={{
        flex: 1,
        padding: '20px',
        overflowY: 'auto',
        display: 'flex',
        flexDirection: 'column',
        gap: '16px'
      }}>
        {messages.map((message) => (
          <div
            key={message.id}
            style={{
              display: 'flex',
              justifyContent: message.type === 'user' ? 'flex-end' : 'flex-start'
            }}
          >
            <div style={{
              maxWidth: '80%',
              padding: '12px 16px',
              borderRadius: message.type === 'user' ? '16px 16px 4px 16px' : '16px 16px 16px 4px',
              background: message.type === 'user'
                ? `linear-gradient(135deg, ${colors.primary} 0%, ${colors.primaryLight} 100%)`
                : colors.warmGlow + '40',
              color: message.type === 'user' ? 'white' : colors.text.primary
            }}>
              <p className="sf-pro" style={{
                fontSize: '14px',
                lineHeight: '1.4',
                margin: 0,
                fontWeight: message.type === 'user' ? '500' : '400'
              }}>
                {message.content}
              </p>

              {message.status && (
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '6px',
                  marginTop: '8px',
                  fontSize: '12px',
                  opacity: 0.8
                }}>
                  {message.status === 'posted' && <CheckCircle size={12} />}
                  {message.status === 'scheduled' && <Clock size={12} />}
                  {message.status === 'sending' && <Loader size={12} className="animate-spin" />}
                  <span>{message.status === 'posted' ? 'Posted' : message.status}</span>
                </div>
              )}
            </div>
          </div>
        ))}

        {isLoading && (
          <div style={{ display: 'flex', justifyContent: 'flex-start' }}>
            <div style={{
              padding: '12px 16px',
              borderRadius: '16px 16px 16px 4px',
              background: colors.warmGlow + '40',
              display: 'flex',
              alignItems: 'center',
              gap: '8px'
            }}>
              <Loader size={14} className="animate-spin" color={colors.primary} />
              <span className="sf-pro" style={{
                fontSize: '14px',
                color: colors.text.secondary
              }}>
                Agent E is thinking...
              </span>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Quick Actions */}
      <div style={{
        padding: '16px 20px 0',
        borderTop: `1px solid ${colors.border}`,
        background: colors.warmGlow + '10'
      }}>
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(2, 1fr)',
          gap: '8px',
          marginBottom: '16px'
        }}>
          {[
            { label: '🚀 Post Now', action: 'post now' },
            { label: '⏰ Schedule', action: 'schedule this' },
            { label: '✨ Improve', action: 'improve this' },
            { label: '🧵 Thread', action: 'make thread' }
          ].map((button, index) => (
            <button
              key={index}
              onClick={() => setInputValue(button.action)}
              className="sf-pro"
              style={{
                padding: '8px 12px',
                background: colors.surface,
                border: `1px solid ${colors.border}`,
                borderRadius: '8px',
                fontSize: '12px',
                fontWeight: '500',
                color: colors.text.secondary,
                cursor: 'pointer',
                transition: 'all 0.2s ease'
              }}
              onMouseEnter={(e) => {
                const target = e.target as HTMLButtonElement;
                target.style.background = colors.warmGlow + '30';
                target.style.borderColor = colors.primary + '40';
              }}
              onMouseLeave={(e) => {
                const target = e.target as HTMLButtonElement;
                target.style.background = colors.surface;
                target.style.borderColor = colors.border;
              }}
            >
              {button.label}
            </button>
          ))}
        </div>
      </div>

      {/* Input */}
      <div style={{
        padding: '0 20px 20px',
        background: colors.warmGlow + '10'
      }}>
        <div style={{
          display: 'flex',
          gap: '12px',
          alignItems: 'flex-end'
        }}>
          <textarea
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Ask Agent E to schedule, improve, or post your content..."
            className="sf-pro"
            rows={2}
            style={{
              flex: 1,
              padding: '12px',
              border: `1px solid ${colors.border}`,
              borderRadius: '12px',
              fontSize: '14px',
              background: colors.surface,
              resize: 'none',
              outline: 'none'
            }}
          />
          <button
            onClick={handleSendMessage}
            disabled={!inputValue.trim() || isLoading}
            style={{
              padding: '12px',
              background: `linear-gradient(135deg, ${colors.primary} 0%, ${colors.primaryLight} 100%)`,
              color: 'white',
              border: 'none',
              borderRadius: '12px',
              cursor: inputValue.trim() && !isLoading ? 'pointer' : 'not-allowed',
              opacity: inputValue.trim() && !isLoading ? 1 : 0.5,
              transition: 'all 0.2s ease'
            }}
          >
            <Send size={16} />
          </button>
        </div>
      </div>
    </div>
  );
};

export default AgentEChat;
