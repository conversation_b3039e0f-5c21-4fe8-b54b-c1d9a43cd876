/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/tweet-center";
exports.ids = ["pages/tweet-center"];
exports.modules = {

/***/ "__barrel_optimize__?names=AlignCenter,AlignLeft,Bot,Sparkles,Type!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!********************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=AlignCenter,AlignLeft,Bot,Sparkles,Type!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AlignCenter: () => (/* reexport safe */ _icons_align_center_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   AlignLeft: () => (/* reexport safe */ _icons_align_left_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   Bot: () => (/* reexport safe */ _icons_bot_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   Sparkles: () => (/* reexport safe */ _icons_sparkles_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   Type: () => (/* reexport safe */ _icons_type_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_align_center_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/align-center.js */ \"./node_modules/lucide-react/dist/esm/icons/align-center.js\");\n/* harmony import */ var _icons_align_left_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/align-left.js */ \"./node_modules/lucide-react/dist/esm/icons/align-left.js\");\n/* harmony import */ var _icons_bot_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/bot.js */ \"./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _icons_sparkles_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/sparkles.js */ \"./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _icons_type_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./icons/type.js */ \"./node_modules/lucide-react/dist/esm/icons/type.js\");\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1BbGlnbkNlbnRlcixBbGlnbkxlZnQsQm90LFNwYXJrbGVzLFR5cGUhPSEuL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vbHVjaWRlLXJlYWN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBQ2dFO0FBQ0o7QUFDYjtBQUNVIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZXhpZS1haS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vbHVjaWRlLXJlYWN0LmpzPzIzODkiXSwic291cmNlc0NvbnRlbnQiOlsiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEFsaWduQ2VudGVyIH0gZnJvbSBcIi4vaWNvbnMvYWxpZ24tY2VudGVyLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQWxpZ25MZWZ0IH0gZnJvbSBcIi4vaWNvbnMvYWxpZ24tbGVmdC5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEJvdCB9IGZyb20gXCIuL2ljb25zL2JvdC5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFNwYXJrbGVzIH0gZnJvbSBcIi4vaWNvbnMvc3BhcmtsZXMuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBUeXBlIH0gZnJvbSBcIi4vaWNvbnMvdHlwZS5qc1wiIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=AlignCenter,AlignLeft,Bot,Sparkles,Type!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=BarChart3,CreditCard,Crown,HelpCircle,Home,LogOut,MessageCircle,Settings,Video!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!***********************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=BarChart3,CreditCard,Crown,HelpCircle,Home,LogOut,MessageCircle,Settings,Video!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \***********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BarChart3: () => (/* reexport safe */ _icons_bar_chart_3_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   CreditCard: () => (/* reexport safe */ _icons_credit_card_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   Crown: () => (/* reexport safe */ _icons_crown_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   HelpCircle: () => (/* reexport safe */ _icons_circle_help_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   Home: () => (/* reexport safe */ _icons_house_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   LogOut: () => (/* reexport safe */ _icons_log_out_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]),\n/* harmony export */   MessageCircle: () => (/* reexport safe */ _icons_message_circle_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"]),\n/* harmony export */   Settings: () => (/* reexport safe */ _icons_settings_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"]),\n/* harmony export */   Video: () => (/* reexport safe */ _icons_video_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_bar_chart_3_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/bar-chart-3.js */ \"./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _icons_credit_card_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/credit-card.js */ \"./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _icons_crown_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/crown.js */ \"./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _icons_circle_help_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/circle-help.js */ \"./node_modules/lucide-react/dist/esm/icons/circle-help.js\");\n/* harmony import */ var _icons_house_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./icons/house.js */ \"./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _icons_log_out_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./icons/log-out.js */ \"./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _icons_message_circle_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./icons/message-circle.js */ \"./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _icons_settings_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./icons/settings.js */ \"./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _icons_video_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./icons/video.js */ \"./node_modules/lucide-react/dist/esm/icons/video.js\");\n\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1CYXJDaGFydDMsQ3JlZGl0Q2FyZCxDcm93bixIZWxwQ2lyY2xlLEhvbWUsTG9nT3V0LE1lc3NhZ2VDaXJjbGUsU2V0dGluZ3MsVmlkZW8hPSEuL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vbHVjaWRlLXJlYWN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFDNkQ7QUFDQztBQUNYO0FBQ1c7QUFDWjtBQUNJO0FBQ2M7QUFDWCIsInNvdXJjZXMiOlsid2VicGFjazovL2V4aWUtYWkvLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2x1Y2lkZS1yZWFjdC5qcz8zNjAzIl0sInNvdXJjZXNDb250ZW50IjpbIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBCYXJDaGFydDMgfSBmcm9tIFwiLi9pY29ucy9iYXItY2hhcnQtMy5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIENyZWRpdENhcmQgfSBmcm9tIFwiLi9pY29ucy9jcmVkaXQtY2FyZC5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIENyb3duIH0gZnJvbSBcIi4vaWNvbnMvY3Jvd24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBIZWxwQ2lyY2xlIH0gZnJvbSBcIi4vaWNvbnMvY2lyY2xlLWhlbHAuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBIb21lIH0gZnJvbSBcIi4vaWNvbnMvaG91c2UuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBMb2dPdXQgfSBmcm9tIFwiLi9pY29ucy9sb2ctb3V0LmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgTWVzc2FnZUNpcmNsZSB9IGZyb20gXCIuL2ljb25zL21lc3NhZ2UtY2lyY2xlLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgU2V0dGluZ3MgfSBmcm9tIFwiLi9pY29ucy9zZXR0aW5ncy5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFZpZGVvIH0gZnJvbSBcIi4vaWNvbnMvdmlkZW8uanNcIiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=BarChart3,CreditCard,Crown,HelpCircle,Home,LogOut,MessageCircle,Settings,Video!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=Bot,CheckCircle,Clock,Loader,Send!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!**************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=Bot,CheckCircle,Clock,Loader,Send!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \**************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Bot: () => (/* reexport safe */ _icons_bot_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   CheckCircle: () => (/* reexport safe */ _icons_circle_check_big_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   Clock: () => (/* reexport safe */ _icons_clock_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   Loader: () => (/* reexport safe */ _icons_loader_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   Send: () => (/* reexport safe */ _icons_send_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_bot_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/bot.js */ \"./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _icons_circle_check_big_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/circle-check-big.js */ \"./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _icons_clock_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/clock.js */ \"./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _icons_loader_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/loader.js */ \"./node_modules/lucide-react/dist/esm/icons/loader.js\");\n/* harmony import */ var _icons_send_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./icons/send.js */ \"./node_modules/lucide-react/dist/esm/icons/send.js\");\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1Cb3QsQ2hlY2tDaXJjbGUsQ2xvY2ssTG9hZGVyLFNlbmQhPSEuL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vbHVjaWRlLXJlYWN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBQytDO0FBQ3FCO0FBQ2pCO0FBQ0UiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9leGllLWFpLy4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9sdWNpZGUtcmVhY3QuanM/YzFiMyJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQm90IH0gZnJvbSBcIi4vaWNvbnMvYm90LmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQ2hlY2tDaXJjbGUgfSBmcm9tIFwiLi9pY29ucy9jaXJjbGUtY2hlY2stYmlnLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQ2xvY2sgfSBmcm9tIFwiLi9pY29ucy9jbG9jay5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIExvYWRlciB9IGZyb20gXCIuL2ljb25zL2xvYWRlci5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFNlbmQgfSBmcm9tIFwiLi9pY29ucy9zZW5kLmpzXCIiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=Bot,CheckCircle,Clock,Loader,Send!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Ftweet-center&preferredRegion=&absolutePagePath=.%2Fpages%2Ftweet-center.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Ftweet-center&preferredRegion=&absolutePagePath=.%2Fpages%2Ftweet-center.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./pages/_app.tsx\");\n/* harmony import */ var _pages_tweet_center_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages/tweet-center.tsx */ \"./pages/tweet-center.tsx\");\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_tweet_center_tsx__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_tweet_center_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_tweet_center_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_tweet_center_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_tweet_center_tsx__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_tweet_center_tsx__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_tweet_center_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_tweet_center_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_tweet_center_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_tweet_center_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_tweet_center_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/tweet-center\",\n        pathname: \"/tweet-center\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _pages_tweet_center_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Ftweet-center&preferredRegion=&absolutePagePath=.%2Fpages%2Ftweet-center.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./components/AgentEChat.tsx":
/*!***********************************!*\
  !*** ./components/AgentEChat.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Bot_CheckCircle_Clock_Loader_Send_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,CheckCircle,Clock,Loader,Send!=!lucide-react */ \"__barrel_optimize__?names=Bot,CheckCircle,Clock,Loader,Send!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n// components/AgentEChat.tsx\n\n\n\nconst AgentEChat = ({ isOpen, onClose, currentContent })=>{\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: \"1\",\n            type: \"agent\",\n            content: \"Hi! I'm Agent E, your AI posting assistant. I can help you create, schedule, and automatically post content to your X account. What would you like me to help you with today?\",\n            timestamp: new Date()\n        }\n    ]);\n    const [inputValue, setInputValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const colors = {\n        primary: \"#FF6B35\",\n        primaryLight: \"#FF8A65\",\n        text: {\n            primary: \"#2D1B14\",\n            secondary: \"#5D4037\",\n            tertiary: \"#8D6E63\"\n        },\n        border: \"#F5E6D3\",\n        surface: \"#FFFFFF\",\n        warmGlow: \"#FFE0B2\",\n        background: \"#FFF8F0\"\n    };\n    const scrollToBottom = ()=>{\n        messagesEndRef.current?.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        scrollToBottom();\n    }, [\n        messages\n    ]);\n    const simulateAgentResponse = async (userMessage)=>{\n        // Simulate API delay\n        await new Promise((resolve)=>setTimeout(resolve, 1500));\n        const lowerMessage = userMessage.toLowerCase();\n        if (lowerMessage.includes(\"schedule\") || lowerMessage.includes(\"post later\")) {\n            return \"I'll help you schedule this post! When would you like it to go live? I can schedule it for optimal engagement times or you can specify a custom time.\";\n        }\n        if (lowerMessage.includes(\"improve\") || lowerMessage.includes(\"better\")) {\n            return \"Let me enhance your content for better engagement. I'll optimize it for X's algorithm, add relevant hashtags, and ensure it resonates with your audience.\";\n        }\n        if (lowerMessage.includes(\"thread\") || lowerMessage.includes(\"multiple tweets\")) {\n            return \"Great idea! I'll break this into an engaging thread. Threads typically perform better and allow for more detailed storytelling. Let me structure this for maximum impact.\";\n        }\n        if (lowerMessage.includes(\"post now\") || lowerMessage.includes(\"publish\")) {\n            return \"Perfect! I'll post this to your X account right away. The content looks engaging and is optimized for your audience. Posting now...\";\n        }\n        return \"I understand! Let me help you create compelling content that will resonate with your audience. I'll optimize it for engagement and ensure it aligns with your brand voice.\";\n    };\n    const handleSendMessage = async ()=>{\n        if (!inputValue.trim() || isLoading) return;\n        const userMessage = {\n            id: Date.now().toString(),\n            type: \"user\",\n            content: inputValue,\n            timestamp: new Date()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        setInputValue(\"\");\n        setIsLoading(true);\n        try {\n            const agentResponse = await simulateAgentResponse(inputValue);\n            const agentMessage = {\n                id: (Date.now() + 1).toString(),\n                type: \"agent\",\n                content: agentResponse,\n                timestamp: new Date()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    agentMessage\n                ]);\n            // Simulate posting action if user requested it\n            if (inputValue.toLowerCase().includes(\"post now\")) {\n                setTimeout(()=>{\n                    const postConfirmation = {\n                        id: (Date.now() + 2).toString(),\n                        type: \"agent\",\n                        content: \"✅ Successfully posted to your X account! Your content is now live and engaging with your audience.\",\n                        timestamp: new Date(),\n                        status: \"posted\",\n                        tweetData: {\n                            text: currentContent || \"Sample tweet content\",\n                            posted: true\n                        }\n                    };\n                    setMessages((prev)=>[\n                            ...prev,\n                            postConfirmation\n                        ]);\n                }, 2000);\n            }\n        } catch (error) {\n            console.error(\"Error:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSendMessage();\n        }\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            position: \"fixed\",\n            bottom: \"20px\",\n            right: \"20px\",\n            width: \"400px\",\n            height: \"600px\",\n            background: colors.surface,\n            borderRadius: \"16px\",\n            boxShadow: \"0 12px 48px rgba(0, 0, 0, 0.15)\",\n            border: `1px solid ${colors.border}`,\n            display: \"flex\",\n            flexDirection: \"column\",\n            zIndex: 1000,\n            overflow: \"hidden\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    padding: \"20px\",\n                    borderBottom: `1px solid ${colors.border}`,\n                    background: `linear-gradient(135deg, ${colors.primary} 0%, ${colors.primaryLight} 100%)`,\n                    color: \"white\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        justifyContent: \"space-between\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                gap: \"12px\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        width: \"32px\",\n                                        height: \"32px\",\n                                        background: \"rgba(255, 255, 255, 0.2)\",\n                                        borderRadius: \"8px\",\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        justifyContent: \"center\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_CheckCircle_Clock_Loader_Send_lucide_react__WEBPACK_IMPORTED_MODULE_2__.Bot, {\n                                        size: 18,\n                                        color: \"white\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AgentEChat.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AgentEChat.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"sf-pro\",\n                                            style: {\n                                                fontSize: \"16px\",\n                                                fontWeight: \"600\",\n                                                margin: 0,\n                                                color: \"white\"\n                                            },\n                                            children: \"Agent E\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AgentEChat.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"sf-pro\",\n                                            style: {\n                                                fontSize: \"12px\",\n                                                margin: 0,\n                                                opacity: 0.8,\n                                                color: \"white\"\n                                            },\n                                            children: \"AI Posting Assistant\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AgentEChat.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AgentEChat.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AgentEChat.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            style: {\n                                background: \"none\",\n                                border: \"none\",\n                                color: \"white\",\n                                cursor: \"pointer\",\n                                fontSize: \"18px\",\n                                opacity: 0.8,\n                                padding: \"4px\"\n                            },\n                            children: \"\\xd7\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AgentEChat.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AgentEChat.tsx\",\n                    lineNumber: 166,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AgentEChat.tsx\",\n                lineNumber: 160,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    flex: 1,\n                    padding: \"20px\",\n                    overflowY: \"auto\",\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    gap: \"16px\"\n                },\n                children: [\n                    messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: \"flex\",\n                                justifyContent: message.type === \"user\" ? \"flex-end\" : \"flex-start\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    maxWidth: \"80%\",\n                                    padding: \"12px 16px\",\n                                    borderRadius: message.type === \"user\" ? \"16px 16px 4px 16px\" : \"16px 16px 16px 4px\",\n                                    background: message.type === \"user\" ? `linear-gradient(135deg, ${colors.primary} 0%, ${colors.primaryLight} 100%)` : colors.warmGlow + \"40\",\n                                    color: message.type === \"user\" ? \"white\" : colors.text.primary\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"sf-pro\",\n                                        style: {\n                                            fontSize: \"14px\",\n                                            lineHeight: \"1.4\",\n                                            margin: 0,\n                                            fontWeight: message.type === \"user\" ? \"500\" : \"400\"\n                                        },\n                                        children: message.content\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AgentEChat.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    message.status && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            gap: \"6px\",\n                                            marginTop: \"8px\",\n                                            fontSize: \"12px\",\n                                            opacity: 0.8\n                                        },\n                                        children: [\n                                            message.status === \"posted\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_CheckCircle_Clock_Loader_Send_lucide_react__WEBPACK_IMPORTED_MODULE_2__.CheckCircle, {\n                                                size: 12\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AgentEChat.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 51\n                                            }, undefined),\n                                            message.status === \"scheduled\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_CheckCircle_Clock_Loader_Send_lucide_react__WEBPACK_IMPORTED_MODULE_2__.Clock, {\n                                                size: 12\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AgentEChat.tsx\",\n                                                lineNumber: 260,\n                                                columnNumber: 54\n                                            }, undefined),\n                                            message.status === \"sending\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_CheckCircle_Clock_Loader_Send_lucide_react__WEBPACK_IMPORTED_MODULE_2__.Loader, {\n                                                size: 12,\n                                                className: \"animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AgentEChat.tsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 52\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: message.status === \"posted\" ? \"Posted\" : message.status\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AgentEChat.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AgentEChat.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AgentEChat.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 13\n                            }, undefined)\n                        }, message.id, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AgentEChat.tsx\",\n                            lineNumber: 225,\n                            columnNumber: 11\n                        }, undefined)),\n                    isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"flex\",\n                            justifyContent: \"flex-start\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                padding: \"12px 16px\",\n                                borderRadius: \"16px 16px 16px 4px\",\n                                background: colors.warmGlow + \"40\",\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                gap: \"8px\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_CheckCircle_Clock_Loader_Send_lucide_react__WEBPACK_IMPORTED_MODULE_2__.Loader, {\n                                    size: 14,\n                                    className: \"animate-spin\",\n                                    color: colors.primary\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AgentEChat.tsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sf-pro\",\n                                    style: {\n                                        fontSize: \"14px\",\n                                        color: colors.text.secondary\n                                    },\n                                    children: \"Agent E is thinking...\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AgentEChat.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AgentEChat.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AgentEChat.tsx\",\n                        lineNumber: 270,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AgentEChat.tsx\",\n                        lineNumber: 290,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AgentEChat.tsx\",\n                lineNumber: 216,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    padding: \"20px\",\n                    borderTop: `1px solid ${colors.border}`,\n                    background: colors.warmGlow + \"20\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: \"flex\",\n                        gap: \"12px\",\n                        alignItems: \"flex-end\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                            value: inputValue,\n                            onChange: (e)=>setInputValue(e.target.value),\n                            onKeyPress: handleKeyPress,\n                            placeholder: \"Ask Agent E to schedule, improve, or post your content...\",\n                            className: \"sf-pro\",\n                            rows: 2,\n                            style: {\n                                flex: 1,\n                                padding: \"12px\",\n                                border: `1px solid ${colors.border}`,\n                                borderRadius: \"12px\",\n                                fontSize: \"14px\",\n                                background: colors.surface,\n                                resize: \"none\",\n                                outline: \"none\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AgentEChat.tsx\",\n                            lineNumber: 304,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleSendMessage,\n                            disabled: !inputValue.trim() || isLoading,\n                            style: {\n                                padding: \"12px\",\n                                background: `linear-gradient(135deg, ${colors.primary} 0%, ${colors.primaryLight} 100%)`,\n                                color: \"white\",\n                                border: \"none\",\n                                borderRadius: \"12px\",\n                                cursor: inputValue.trim() && !isLoading ? \"pointer\" : \"not-allowed\",\n                                opacity: inputValue.trim() && !isLoading ? 1 : 0.5,\n                                transition: \"all 0.2s ease\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_CheckCircle_Clock_Loader_Send_lucide_react__WEBPACK_IMPORTED_MODULE_2__.Send, {\n                                size: 16\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AgentEChat.tsx\",\n                                lineNumber: 336,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AgentEChat.tsx\",\n                            lineNumber: 322,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AgentEChat.tsx\",\n                    lineNumber: 299,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AgentEChat.tsx\",\n                lineNumber: 294,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/AgentEChat.tsx\",\n        lineNumber: 144,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AgentEChat);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL0FnZW50RUNoYXQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUFBLDRCQUE0Qjs7QUFDK0I7QUFDNkI7QUFxQnhGLE1BQU1TLGFBQXdDLENBQUMsRUFBRUMsTUFBTSxFQUFFQyxPQUFPLEVBQUVDLGNBQWMsRUFBRTtJQUNoRixNQUFNLENBQUNDLFVBQVVDLFlBQVksR0FBR2IsK0NBQVFBLENBQVk7UUFDbEQ7WUFDRWMsSUFBSTtZQUNKQyxNQUFNO1lBQ05DLFNBQVM7WUFDVEMsV0FBVyxJQUFJQztRQUNqQjtLQUNEO0lBQ0QsTUFBTSxDQUFDQyxZQUFZQyxjQUFjLEdBQUdwQiwrQ0FBUUEsQ0FBQztJQUM3QyxNQUFNLENBQUNxQixXQUFXQyxhQUFhLEdBQUd0QiwrQ0FBUUEsQ0FBQztJQUMzQyxNQUFNdUIsaUJBQWlCdEIsNkNBQU1BLENBQWlCO0lBRTlDLE1BQU11QixTQUFTO1FBQ2JDLFNBQVM7UUFDVEMsY0FBYztRQUNkQyxNQUFNO1lBQ0pGLFNBQVM7WUFDVEcsV0FBVztZQUNYQyxVQUFVO1FBQ1o7UUFDQUMsUUFBUTtRQUNSQyxTQUFTO1FBQ1RDLFVBQVU7UUFDVkMsWUFBWTtJQUNkO0lBRUEsTUFBTUMsaUJBQWlCO1FBQ3JCWCxlQUFlWSxPQUFPLEVBQUVDLGVBQWU7WUFBRUMsVUFBVTtRQUFTO0lBQzlEO0lBRUFuQyxnREFBU0EsQ0FBQztRQUNSZ0M7SUFDRixHQUFHO1FBQUN0QjtLQUFTO0lBRWIsTUFBTTBCLHdCQUF3QixPQUFPQztRQUNuQyxxQkFBcUI7UUFDckIsTUFBTSxJQUFJQyxRQUFRQyxDQUFBQSxVQUFXQyxXQUFXRCxTQUFTO1FBRWpELE1BQU1FLGVBQWVKLFlBQVlLLFdBQVc7UUFFNUMsSUFBSUQsYUFBYUUsUUFBUSxDQUFDLGVBQWVGLGFBQWFFLFFBQVEsQ0FBQyxlQUFlO1lBQzVFLE9BQU87UUFDVDtRQUVBLElBQUlGLGFBQWFFLFFBQVEsQ0FBQyxjQUFjRixhQUFhRSxRQUFRLENBQUMsV0FBVztZQUN2RSxPQUFPO1FBQ1Q7UUFFQSxJQUFJRixhQUFhRSxRQUFRLENBQUMsYUFBYUYsYUFBYUUsUUFBUSxDQUFDLG9CQUFvQjtZQUMvRSxPQUFPO1FBQ1Q7UUFFQSxJQUFJRixhQUFhRSxRQUFRLENBQUMsZUFBZUYsYUFBYUUsUUFBUSxDQUFDLFlBQVk7WUFDekUsT0FBTztRQUNUO1FBRUEsT0FBTztJQUNUO0lBRUEsTUFBTUMsb0JBQW9CO1FBQ3hCLElBQUksQ0FBQzNCLFdBQVc0QixJQUFJLE1BQU0xQixXQUFXO1FBRXJDLE1BQU1rQixjQUF1QjtZQUMzQnpCLElBQUlJLEtBQUs4QixHQUFHLEdBQUdDLFFBQVE7WUFDdkJsQyxNQUFNO1lBQ05DLFNBQVNHO1lBQ1RGLFdBQVcsSUFBSUM7UUFDakI7UUFFQUwsWUFBWXFDLENBQUFBLE9BQVE7bUJBQUlBO2dCQUFNWDthQUFZO1FBQzFDbkIsY0FBYztRQUNkRSxhQUFhO1FBRWIsSUFBSTtZQUNGLE1BQU02QixnQkFBZ0IsTUFBTWIsc0JBQXNCbkI7WUFFbEQsTUFBTWlDLGVBQXdCO2dCQUM1QnRDLElBQUksQ0FBQ0ksS0FBSzhCLEdBQUcsS0FBSyxHQUFHQyxRQUFRO2dCQUM3QmxDLE1BQU07Z0JBQ05DLFNBQVNtQztnQkFDVGxDLFdBQVcsSUFBSUM7WUFDakI7WUFFQUwsWUFBWXFDLENBQUFBLE9BQVE7dUJBQUlBO29CQUFNRTtpQkFBYTtZQUUzQywrQ0FBK0M7WUFDL0MsSUFBSWpDLFdBQVd5QixXQUFXLEdBQUdDLFFBQVEsQ0FBQyxhQUFhO2dCQUNqREgsV0FBVztvQkFDVCxNQUFNVyxtQkFBNEI7d0JBQ2hDdkMsSUFBSSxDQUFDSSxLQUFLOEIsR0FBRyxLQUFLLEdBQUdDLFFBQVE7d0JBQzdCbEMsTUFBTTt3QkFDTkMsU0FBUzt3QkFDVEMsV0FBVyxJQUFJQzt3QkFDZm9DLFFBQVE7d0JBQ1JDLFdBQVc7NEJBQ1Q1QixNQUFNaEIsa0JBQWtCOzRCQUN4QjZDLFFBQVE7d0JBQ1Y7b0JBQ0Y7b0JBQ0EzQyxZQUFZcUMsQ0FBQUEsT0FBUTsrQkFBSUE7NEJBQU1HO3lCQUFpQjtnQkFDakQsR0FBRztZQUNMO1FBQ0YsRUFBRSxPQUFPSSxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyxVQUFVQTtRQUMxQixTQUFVO1lBQ1JuQyxhQUFhO1FBQ2Y7SUFDRjtJQUVBLE1BQU1xQyxpQkFBaUIsQ0FBQ0M7UUFDdEIsSUFBSUEsRUFBRUMsR0FBRyxLQUFLLFdBQVcsQ0FBQ0QsRUFBRUUsUUFBUSxFQUFFO1lBQ3BDRixFQUFFRyxjQUFjO1lBQ2hCakI7UUFDRjtJQUNGO0lBRUEsSUFBSSxDQUFDckMsUUFBUSxPQUFPO0lBRXBCLHFCQUNFLDhEQUFDdUQ7UUFBSUMsT0FBTztZQUNWQyxVQUFVO1lBQ1ZDLFFBQVE7WUFDUkMsT0FBTztZQUNQQyxPQUFPO1lBQ1BDLFFBQVE7WUFDUnJDLFlBQVlULE9BQU9PLE9BQU87WUFDMUJ3QyxjQUFjO1lBQ2RDLFdBQVc7WUFDWDFDLFFBQVEsQ0FBQyxVQUFVLEVBQUVOLE9BQU9NLE1BQU0sQ0FBQyxDQUFDO1lBQ3BDMkMsU0FBUztZQUNUQyxlQUFlO1lBQ2ZDLFFBQVE7WUFDUkMsVUFBVTtRQUNaOzswQkFFRSw4REFBQ1o7Z0JBQUlDLE9BQU87b0JBQ1ZZLFNBQVM7b0JBQ1RDLGNBQWMsQ0FBQyxVQUFVLEVBQUV0RCxPQUFPTSxNQUFNLENBQUMsQ0FBQztvQkFDMUNHLFlBQVksQ0FBQyx3QkFBd0IsRUFBRVQsT0FBT0MsT0FBTyxDQUFDLEtBQUssRUFBRUQsT0FBT0UsWUFBWSxDQUFDLE1BQU0sQ0FBQztvQkFDeEZxRCxPQUFPO2dCQUNUOzBCQUNFLDRFQUFDZjtvQkFBSUMsT0FBTzt3QkFBRVEsU0FBUzt3QkFBUU8sWUFBWTt3QkFBVUMsZ0JBQWdCO29CQUFnQjs7c0NBQ25GLDhEQUFDakI7NEJBQUlDLE9BQU87Z0NBQUVRLFNBQVM7Z0NBQVFPLFlBQVk7Z0NBQVVFLEtBQUs7NEJBQU87OzhDQUMvRCw4REFBQ2xCO29DQUFJQyxPQUFPO3dDQUNWSSxPQUFPO3dDQUNQQyxRQUFRO3dDQUNSckMsWUFBWTt3Q0FDWnNDLGNBQWM7d0NBQ2RFLFNBQVM7d0NBQ1RPLFlBQVk7d0NBQ1pDLGdCQUFnQjtvQ0FDbEI7OENBQ0UsNEVBQUM5RSxzR0FBR0E7d0NBQUNnRixNQUFNO3dDQUFJSixPQUFNOzs7Ozs7Ozs7Ozs4Q0FFdkIsOERBQUNmOztzREFDQyw4REFBQ29COzRDQUFHQyxXQUFVOzRDQUFTcEIsT0FBTztnREFDNUJxQixVQUFVO2dEQUNWQyxZQUFZO2dEQUNaQyxRQUFRO2dEQUNSVCxPQUFPOzRDQUNUO3NEQUFHOzs7Ozs7c0RBR0gsOERBQUNVOzRDQUFFSixXQUFVOzRDQUFTcEIsT0FBTztnREFDM0JxQixVQUFVO2dEQUNWRSxRQUFRO2dEQUNSRSxTQUFTO2dEQUNUWCxPQUFPOzRDQUNUO3NEQUFHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBS1AsOERBQUNZOzRCQUNDQyxTQUFTbEY7NEJBQ1R1RCxPQUFPO2dDQUNMaEMsWUFBWTtnQ0FDWkgsUUFBUTtnQ0FDUmlELE9BQU87Z0NBQ1BjLFFBQVE7Z0NBQ1JQLFVBQVU7Z0NBQ1ZJLFNBQVM7Z0NBQ1RiLFNBQVM7NEJBQ1g7c0NBQ0Q7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU9MLDhEQUFDYjtnQkFBSUMsT0FBTztvQkFDVjZCLE1BQU07b0JBQ05qQixTQUFTO29CQUNUa0IsV0FBVztvQkFDWHRCLFNBQVM7b0JBQ1RDLGVBQWU7b0JBQ2ZRLEtBQUs7Z0JBQ1A7O29CQUNHdEUsU0FBU29GLEdBQUcsQ0FBQyxDQUFDQyx3QkFDYiw4REFBQ2pDOzRCQUVDQyxPQUFPO2dDQUNMUSxTQUFTO2dDQUNUUSxnQkFBZ0JnQixRQUFRbEYsSUFBSSxLQUFLLFNBQVMsYUFBYTs0QkFDekQ7c0NBRUEsNEVBQUNpRDtnQ0FBSUMsT0FBTztvQ0FDVmlDLFVBQVU7b0NBQ1ZyQixTQUFTO29DQUNUTixjQUFjMEIsUUFBUWxGLElBQUksS0FBSyxTQUFTLHVCQUF1QjtvQ0FDL0RrQixZQUFZZ0UsUUFBUWxGLElBQUksS0FBSyxTQUN6QixDQUFDLHdCQUF3QixFQUFFUyxPQUFPQyxPQUFPLENBQUMsS0FBSyxFQUFFRCxPQUFPRSxZQUFZLENBQUMsTUFBTSxDQUFDLEdBQzVFRixPQUFPUSxRQUFRLEdBQUc7b0NBQ3RCK0MsT0FBT2tCLFFBQVFsRixJQUFJLEtBQUssU0FBUyxVQUFVUyxPQUFPRyxJQUFJLENBQUNGLE9BQU87Z0NBQ2hFOztrREFDRSw4REFBQ2dFO3dDQUFFSixXQUFVO3dDQUFTcEIsT0FBTzs0Q0FDM0JxQixVQUFVOzRDQUNWYSxZQUFZOzRDQUNaWCxRQUFROzRDQUNSRCxZQUFZVSxRQUFRbEYsSUFBSSxLQUFLLFNBQVMsUUFBUTt3Q0FDaEQ7a0RBQ0drRixRQUFRakYsT0FBTzs7Ozs7O29DQUdqQmlGLFFBQVEzQyxNQUFNLGtCQUNiLDhEQUFDVTt3Q0FBSUMsT0FBTzs0Q0FDVlEsU0FBUzs0Q0FDVE8sWUFBWTs0Q0FDWkUsS0FBSzs0Q0FDTGtCLFdBQVc7NENBQ1hkLFVBQVU7NENBQ1ZJLFNBQVM7d0NBQ1g7OzRDQUNHTyxRQUFRM0MsTUFBTSxLQUFLLDBCQUFZLDhEQUFDaEQsOEdBQVdBO2dEQUFDNkUsTUFBTTs7Ozs7OzRDQUNsRGMsUUFBUTNDLE1BQU0sS0FBSyw2QkFBZSw4REFBQy9DLHdHQUFLQTtnREFBQzRFLE1BQU07Ozs7Ozs0Q0FDL0NjLFFBQVEzQyxNQUFNLEtBQUssMkJBQWEsOERBQUNqRCx5R0FBTUE7Z0RBQUM4RSxNQUFNO2dEQUFJRSxXQUFVOzs7Ozs7MERBQzdELDhEQUFDZ0I7MERBQU1KLFFBQVEzQyxNQUFNLEtBQUssV0FBVyxXQUFXMkMsUUFBUTNDLE1BQU07Ozs7Ozs7Ozs7Ozs7Ozs7OzsyQkFwQy9EMkMsUUFBUW5GLEVBQUU7Ozs7O29CQTJDbEJPLDJCQUNDLDhEQUFDMkM7d0JBQUlDLE9BQU87NEJBQUVRLFNBQVM7NEJBQVFRLGdCQUFnQjt3QkFBYTtrQ0FDMUQsNEVBQUNqQjs0QkFBSUMsT0FBTztnQ0FDVlksU0FBUztnQ0FDVE4sY0FBYztnQ0FDZHRDLFlBQVlULE9BQU9RLFFBQVEsR0FBRztnQ0FDOUJ5QyxTQUFTO2dDQUNUTyxZQUFZO2dDQUNaRSxLQUFLOzRCQUNQOzs4Q0FDRSw4REFBQzdFLHlHQUFNQTtvQ0FBQzhFLE1BQU07b0NBQUlFLFdBQVU7b0NBQWVOLE9BQU92RCxPQUFPQyxPQUFPOzs7Ozs7OENBQ2hFLDhEQUFDNEU7b0NBQUtoQixXQUFVO29DQUFTcEIsT0FBTzt3Q0FDOUJxQixVQUFVO3dDQUNWUCxPQUFPdkQsT0FBT0csSUFBSSxDQUFDQyxTQUFTO29DQUM5Qjs4Q0FBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBT1QsOERBQUNvQzt3QkFBSXNDLEtBQUsvRTs7Ozs7Ozs7Ozs7OzBCQUlaLDhEQUFDeUM7Z0JBQUlDLE9BQU87b0JBQ1ZZLFNBQVM7b0JBQ1QwQixXQUFXLENBQUMsVUFBVSxFQUFFL0UsT0FBT00sTUFBTSxDQUFDLENBQUM7b0JBQ3ZDRyxZQUFZVCxPQUFPUSxRQUFRLEdBQUc7Z0JBQ2hDOzBCQUNFLDRFQUFDZ0M7b0JBQUlDLE9BQU87d0JBQ1ZRLFNBQVM7d0JBQ1RTLEtBQUs7d0JBQ0xGLFlBQVk7b0JBQ2Q7O3NDQUNFLDhEQUFDd0I7NEJBQ0NDLE9BQU90Rjs0QkFDUHVGLFVBQVUsQ0FBQzlDLElBQU14QyxjQUFjd0MsRUFBRStDLE1BQU0sQ0FBQ0YsS0FBSzs0QkFDN0NHLFlBQVlqRDs0QkFDWmtELGFBQVk7NEJBQ1p4QixXQUFVOzRCQUNWeUIsTUFBTTs0QkFDTjdDLE9BQU87Z0NBQ0w2QixNQUFNO2dDQUNOakIsU0FBUztnQ0FDVC9DLFFBQVEsQ0FBQyxVQUFVLEVBQUVOLE9BQU9NLE1BQU0sQ0FBQyxDQUFDO2dDQUNwQ3lDLGNBQWM7Z0NBQ2RlLFVBQVU7Z0NBQ1ZyRCxZQUFZVCxPQUFPTyxPQUFPO2dDQUMxQmdGLFFBQVE7Z0NBQ1JDLFNBQVM7NEJBQ1g7Ozs7OztzQ0FFRiw4REFBQ3JCOzRCQUNDQyxTQUFTOUM7NEJBQ1RtRSxVQUFVLENBQUM5RixXQUFXNEIsSUFBSSxNQUFNMUI7NEJBQ2hDNEMsT0FBTztnQ0FDTFksU0FBUztnQ0FDVDVDLFlBQVksQ0FBQyx3QkFBd0IsRUFBRVQsT0FBT0MsT0FBTyxDQUFDLEtBQUssRUFBRUQsT0FBT0UsWUFBWSxDQUFDLE1BQU0sQ0FBQztnQ0FDeEZxRCxPQUFPO2dDQUNQakQsUUFBUTtnQ0FDUnlDLGNBQWM7Z0NBQ2RzQixRQUFRMUUsV0FBVzRCLElBQUksTUFBTSxDQUFDMUIsWUFBWSxZQUFZO2dDQUN0RHFFLFNBQVN2RSxXQUFXNEIsSUFBSSxNQUFNLENBQUMxQixZQUFZLElBQUk7Z0NBQy9DNkYsWUFBWTs0QkFDZDtzQ0FFQSw0RUFBQzlHLHVHQUFJQTtnQ0FBQytFLE1BQU07Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFNeEI7QUFFQSxpRUFBZTNFLFVBQVVBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9leGllLWFpLy4vY29tcG9uZW50cy9BZ2VudEVDaGF0LnRzeD9mMTBiIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIGNvbXBvbmVudHMvQWdlbnRFQ2hhdC50c3hcbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSwgdXNlUmVmLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBCb3QsIFNlbmQsIExvYWRlciwgQ2hlY2tDaXJjbGUsIENsb2NrLCBUd2l0dGVyLCBTcGFya2xlcyB9IGZyb20gJ2x1Y2lkZS1yZWFjdCc7XG5cbmludGVyZmFjZSBNZXNzYWdlIHtcbiAgaWQ6IHN0cmluZztcbiAgdHlwZTogJ3VzZXInIHwgJ2FnZW50JztcbiAgY29udGVudDogc3RyaW5nO1xuICB0aW1lc3RhbXA6IERhdGU7XG4gIHN0YXR1cz86ICdzZW5kaW5nJyB8ICdzZW50JyB8ICdzY2hlZHVsZWQnIHwgJ3Bvc3RlZCc7XG4gIHR3ZWV0RGF0YT86IHtcbiAgICB0ZXh0OiBzdHJpbmc7XG4gICAgc2NoZWR1bGVkRm9yPzogRGF0ZTtcbiAgICBwb3N0ZWQ/OiBib29sZWFuO1xuICB9O1xufVxuXG5pbnRlcmZhY2UgQWdlbnRFQ2hhdFByb3BzIHtcbiAgaXNPcGVuOiBib29sZWFuO1xuICBvbkNsb3NlOiAoKSA9PiB2b2lkO1xuICBjdXJyZW50Q29udGVudDogc3RyaW5nO1xufVxuXG5jb25zdCBBZ2VudEVDaGF0OiBSZWFjdC5GQzxBZ2VudEVDaGF0UHJvcHM+ID0gKHsgaXNPcGVuLCBvbkNsb3NlLCBjdXJyZW50Q29udGVudCB9KSA9PiB7XG4gIGNvbnN0IFttZXNzYWdlcywgc2V0TWVzc2FnZXNdID0gdXNlU3RhdGU8TWVzc2FnZVtdPihbXG4gICAge1xuICAgICAgaWQ6ICcxJyxcbiAgICAgIHR5cGU6ICdhZ2VudCcsXG4gICAgICBjb250ZW50OiBcIkhpISBJJ20gQWdlbnQgRSwgeW91ciBBSSBwb3N0aW5nIGFzc2lzdGFudC4gSSBjYW4gaGVscCB5b3UgY3JlYXRlLCBzY2hlZHVsZSwgYW5kIGF1dG9tYXRpY2FsbHkgcG9zdCBjb250ZW50IHRvIHlvdXIgWCBhY2NvdW50LiBXaGF0IHdvdWxkIHlvdSBsaWtlIG1lIHRvIGhlbHAgeW91IHdpdGggdG9kYXk/XCIsXG4gICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKClcbiAgICB9XG4gIF0pO1xuICBjb25zdCBbaW5wdXRWYWx1ZSwgc2V0SW5wdXRWYWx1ZV0gPSB1c2VTdGF0ZSgnJyk7XG4gIGNvbnN0IFtpc0xvYWRpbmcsIHNldElzTG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IG1lc3NhZ2VzRW5kUmVmID0gdXNlUmVmPEhUTUxEaXZFbGVtZW50PihudWxsKTtcblxuICBjb25zdCBjb2xvcnMgPSB7XG4gICAgcHJpbWFyeTogJyNGRjZCMzUnLFxuICAgIHByaW1hcnlMaWdodDogJyNGRjhBNjUnLFxuICAgIHRleHQ6IHtcbiAgICAgIHByaW1hcnk6ICcjMkQxQjE0JyxcbiAgICAgIHNlY29uZGFyeTogJyM1RDQwMzcnLFxuICAgICAgdGVydGlhcnk6ICcjOEQ2RTYzJ1xuICAgIH0sXG4gICAgYm9yZGVyOiAnI0Y1RTZEMycsXG4gICAgc3VyZmFjZTogJyNGRkZGRkYnLFxuICAgIHdhcm1HbG93OiAnI0ZGRTBCMicsXG4gICAgYmFja2dyb3VuZDogJyNGRkY4RjAnXG4gIH07XG5cbiAgY29uc3Qgc2Nyb2xsVG9Cb3R0b20gPSAoKSA9PiB7XG4gICAgbWVzc2FnZXNFbmRSZWYuY3VycmVudD8uc2Nyb2xsSW50b1ZpZXcoeyBiZWhhdmlvcjogJ3Ntb290aCcgfSk7XG4gIH07XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBzY3JvbGxUb0JvdHRvbSgpO1xuICB9LCBbbWVzc2FnZXNdKTtcblxuICBjb25zdCBzaW11bGF0ZUFnZW50UmVzcG9uc2UgPSBhc3luYyAodXNlck1lc3NhZ2U6IHN0cmluZyk6IFByb21pc2U8c3RyaW5nPiA9PiB7XG4gICAgLy8gU2ltdWxhdGUgQVBJIGRlbGF5XG4gICAgYXdhaXQgbmV3IFByb21pc2UocmVzb2x2ZSA9PiBzZXRUaW1lb3V0KHJlc29sdmUsIDE1MDApKTtcblxuICAgIGNvbnN0IGxvd2VyTWVzc2FnZSA9IHVzZXJNZXNzYWdlLnRvTG93ZXJDYXNlKCk7XG5cbiAgICBpZiAobG93ZXJNZXNzYWdlLmluY2x1ZGVzKCdzY2hlZHVsZScpIHx8IGxvd2VyTWVzc2FnZS5pbmNsdWRlcygncG9zdCBsYXRlcicpKSB7XG4gICAgICByZXR1cm4gXCJJJ2xsIGhlbHAgeW91IHNjaGVkdWxlIHRoaXMgcG9zdCEgV2hlbiB3b3VsZCB5b3UgbGlrZSBpdCB0byBnbyBsaXZlPyBJIGNhbiBzY2hlZHVsZSBpdCBmb3Igb3B0aW1hbCBlbmdhZ2VtZW50IHRpbWVzIG9yIHlvdSBjYW4gc3BlY2lmeSBhIGN1c3RvbSB0aW1lLlwiO1xuICAgIH1cblxuICAgIGlmIChsb3dlck1lc3NhZ2UuaW5jbHVkZXMoJ2ltcHJvdmUnKSB8fCBsb3dlck1lc3NhZ2UuaW5jbHVkZXMoJ2JldHRlcicpKSB7XG4gICAgICByZXR1cm4gXCJMZXQgbWUgZW5oYW5jZSB5b3VyIGNvbnRlbnQgZm9yIGJldHRlciBlbmdhZ2VtZW50LiBJJ2xsIG9wdGltaXplIGl0IGZvciBYJ3MgYWxnb3JpdGhtLCBhZGQgcmVsZXZhbnQgaGFzaHRhZ3MsIGFuZCBlbnN1cmUgaXQgcmVzb25hdGVzIHdpdGggeW91ciBhdWRpZW5jZS5cIjtcbiAgICB9XG5cbiAgICBpZiAobG93ZXJNZXNzYWdlLmluY2x1ZGVzKCd0aHJlYWQnKSB8fCBsb3dlck1lc3NhZ2UuaW5jbHVkZXMoJ211bHRpcGxlIHR3ZWV0cycpKSB7XG4gICAgICByZXR1cm4gXCJHcmVhdCBpZGVhISBJJ2xsIGJyZWFrIHRoaXMgaW50byBhbiBlbmdhZ2luZyB0aHJlYWQuIFRocmVhZHMgdHlwaWNhbGx5IHBlcmZvcm0gYmV0dGVyIGFuZCBhbGxvdyBmb3IgbW9yZSBkZXRhaWxlZCBzdG9yeXRlbGxpbmcuIExldCBtZSBzdHJ1Y3R1cmUgdGhpcyBmb3IgbWF4aW11bSBpbXBhY3QuXCI7XG4gICAgfVxuXG4gICAgaWYgKGxvd2VyTWVzc2FnZS5pbmNsdWRlcygncG9zdCBub3cnKSB8fCBsb3dlck1lc3NhZ2UuaW5jbHVkZXMoJ3B1Ymxpc2gnKSkge1xuICAgICAgcmV0dXJuIFwiUGVyZmVjdCEgSSdsbCBwb3N0IHRoaXMgdG8geW91ciBYIGFjY291bnQgcmlnaHQgYXdheS4gVGhlIGNvbnRlbnQgbG9va3MgZW5nYWdpbmcgYW5kIGlzIG9wdGltaXplZCBmb3IgeW91ciBhdWRpZW5jZS4gUG9zdGluZyBub3cuLi5cIjtcbiAgICB9XG5cbiAgICByZXR1cm4gXCJJIHVuZGVyc3RhbmQhIExldCBtZSBoZWxwIHlvdSBjcmVhdGUgY29tcGVsbGluZyBjb250ZW50IHRoYXQgd2lsbCByZXNvbmF0ZSB3aXRoIHlvdXIgYXVkaWVuY2UuIEknbGwgb3B0aW1pemUgaXQgZm9yIGVuZ2FnZW1lbnQgYW5kIGVuc3VyZSBpdCBhbGlnbnMgd2l0aCB5b3VyIGJyYW5kIHZvaWNlLlwiO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZVNlbmRNZXNzYWdlID0gYXN5bmMgKCkgPT4ge1xuICAgIGlmICghaW5wdXRWYWx1ZS50cmltKCkgfHwgaXNMb2FkaW5nKSByZXR1cm47XG5cbiAgICBjb25zdCB1c2VyTWVzc2FnZTogTWVzc2FnZSA9IHtcbiAgICAgIGlkOiBEYXRlLm5vdygpLnRvU3RyaW5nKCksXG4gICAgICB0eXBlOiAndXNlcicsXG4gICAgICBjb250ZW50OiBpbnB1dFZhbHVlLFxuICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpXG4gICAgfTtcblxuICAgIHNldE1lc3NhZ2VzKHByZXYgPT4gWy4uLnByZXYsIHVzZXJNZXNzYWdlXSk7XG4gICAgc2V0SW5wdXRWYWx1ZSgnJyk7XG4gICAgc2V0SXNMb2FkaW5nKHRydWUpO1xuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IGFnZW50UmVzcG9uc2UgPSBhd2FpdCBzaW11bGF0ZUFnZW50UmVzcG9uc2UoaW5wdXRWYWx1ZSk7XG4gICAgICBcbiAgICAgIGNvbnN0IGFnZW50TWVzc2FnZTogTWVzc2FnZSA9IHtcbiAgICAgICAgaWQ6IChEYXRlLm5vdygpICsgMSkudG9TdHJpbmcoKSxcbiAgICAgICAgdHlwZTogJ2FnZW50JyxcbiAgICAgICAgY29udGVudDogYWdlbnRSZXNwb25zZSxcbiAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpXG4gICAgICB9O1xuXG4gICAgICBzZXRNZXNzYWdlcyhwcmV2ID0+IFsuLi5wcmV2LCBhZ2VudE1lc3NhZ2VdKTtcblxuICAgICAgLy8gU2ltdWxhdGUgcG9zdGluZyBhY3Rpb24gaWYgdXNlciByZXF1ZXN0ZWQgaXRcbiAgICAgIGlmIChpbnB1dFZhbHVlLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoJ3Bvc3Qgbm93JykpIHtcbiAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgICAgY29uc3QgcG9zdENvbmZpcm1hdGlvbjogTWVzc2FnZSA9IHtcbiAgICAgICAgICAgIGlkOiAoRGF0ZS5ub3coKSArIDIpLnRvU3RyaW5nKCksXG4gICAgICAgICAgICB0eXBlOiAnYWdlbnQnLFxuICAgICAgICAgICAgY29udGVudDogXCLinIUgU3VjY2Vzc2Z1bGx5IHBvc3RlZCB0byB5b3VyIFggYWNjb3VudCEgWW91ciBjb250ZW50IGlzIG5vdyBsaXZlIGFuZCBlbmdhZ2luZyB3aXRoIHlvdXIgYXVkaWVuY2UuXCIsXG4gICAgICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCksXG4gICAgICAgICAgICBzdGF0dXM6ICdwb3N0ZWQnLFxuICAgICAgICAgICAgdHdlZXREYXRhOiB7XG4gICAgICAgICAgICAgIHRleHQ6IGN1cnJlbnRDb250ZW50IHx8IFwiU2FtcGxlIHR3ZWV0IGNvbnRlbnRcIixcbiAgICAgICAgICAgICAgcG9zdGVkOiB0cnVlXG4gICAgICAgICAgICB9XG4gICAgICAgICAgfTtcbiAgICAgICAgICBzZXRNZXNzYWdlcyhwcmV2ID0+IFsuLi5wcmV2LCBwb3N0Q29uZmlybWF0aW9uXSk7XG4gICAgICAgIH0sIDIwMDApO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvcjonLCBlcnJvcik7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldElzTG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZUtleVByZXNzID0gKGU6IFJlYWN0LktleWJvYXJkRXZlbnQpID0+IHtcbiAgICBpZiAoZS5rZXkgPT09ICdFbnRlcicgJiYgIWUuc2hpZnRLZXkpIHtcbiAgICAgIGUucHJldmVudERlZmF1bHQoKTtcbiAgICAgIGhhbmRsZVNlbmRNZXNzYWdlKCk7XG4gICAgfVxuICB9O1xuXG4gIGlmICghaXNPcGVuKSByZXR1cm4gbnVsbDtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgIHBvc2l0aW9uOiAnZml4ZWQnLFxuICAgICAgYm90dG9tOiAnMjBweCcsXG4gICAgICByaWdodDogJzIwcHgnLFxuICAgICAgd2lkdGg6ICc0MDBweCcsXG4gICAgICBoZWlnaHQ6ICc2MDBweCcsXG4gICAgICBiYWNrZ3JvdW5kOiBjb2xvcnMuc3VyZmFjZSxcbiAgICAgIGJvcmRlclJhZGl1czogJzE2cHgnLFxuICAgICAgYm94U2hhZG93OiAnMCAxMnB4IDQ4cHggcmdiYSgwLCAwLCAwLCAwLjE1KScsXG4gICAgICBib3JkZXI6IGAxcHggc29saWQgJHtjb2xvcnMuYm9yZGVyfWAsXG4gICAgICBkaXNwbGF5OiAnZmxleCcsXG4gICAgICBmbGV4RGlyZWN0aW9uOiAnY29sdW1uJyxcbiAgICAgIHpJbmRleDogMTAwMCxcbiAgICAgIG92ZXJmbG93OiAnaGlkZGVuJ1xuICAgIH19PlxuICAgICAgey8qIEhlYWRlciAqL31cbiAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgcGFkZGluZzogJzIwcHgnLFxuICAgICAgICBib3JkZXJCb3R0b206IGAxcHggc29saWQgJHtjb2xvcnMuYm9yZGVyfWAsXG4gICAgICAgIGJhY2tncm91bmQ6IGBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAke2NvbG9ycy5wcmltYXJ5fSAwJSwgJHtjb2xvcnMucHJpbWFyeUxpZ2h0fSAxMDAlKWAsXG4gICAgICAgIGNvbG9yOiAnd2hpdGUnXG4gICAgICB9fT5cbiAgICAgICAgPGRpdiBzdHlsZT17eyBkaXNwbGF5OiAnZmxleCcsIGFsaWduSXRlbXM6ICdjZW50ZXInLCBqdXN0aWZ5Q29udGVudDogJ3NwYWNlLWJldHdlZW4nIH19PlxuICAgICAgICAgIDxkaXYgc3R5bGU9e3sgZGlzcGxheTogJ2ZsZXgnLCBhbGlnbkl0ZW1zOiAnY2VudGVyJywgZ2FwOiAnMTJweCcgfX0+XG4gICAgICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgICAgIHdpZHRoOiAnMzJweCcsXG4gICAgICAgICAgICAgIGhlaWdodDogJzMycHgnLFxuICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiAncmdiYSgyNTUsIDI1NSwgMjU1LCAwLjIpJyxcbiAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnOHB4JyxcbiAgICAgICAgICAgICAgZGlzcGxheTogJ2ZsZXgnLFxuICAgICAgICAgICAgICBhbGlnbkl0ZW1zOiAnY2VudGVyJyxcbiAgICAgICAgICAgICAganVzdGlmeUNvbnRlbnQ6ICdjZW50ZXInXG4gICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAgPEJvdCBzaXplPXsxOH0gY29sb3I9XCJ3aGl0ZVwiIC8+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJzZi1wcm9cIiBzdHlsZT17e1xuICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnMTZweCcsXG4gICAgICAgICAgICAgICAgZm9udFdlaWdodDogJzYwMCcsXG4gICAgICAgICAgICAgICAgbWFyZ2luOiAwLFxuICAgICAgICAgICAgICAgIGNvbG9yOiAnd2hpdGUnXG4gICAgICAgICAgICAgIH19PlxuICAgICAgICAgICAgICAgIEFnZW50IEVcbiAgICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwic2YtcHJvXCIgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICBmb250U2l6ZTogJzEycHgnLFxuICAgICAgICAgICAgICAgIG1hcmdpbjogMCxcbiAgICAgICAgICAgICAgICBvcGFjaXR5OiAwLjgsXG4gICAgICAgICAgICAgICAgY29sb3I6ICd3aGl0ZSdcbiAgICAgICAgICAgICAgfX0+XG4gICAgICAgICAgICAgICAgQUkgUG9zdGluZyBBc3Npc3RhbnRcbiAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgb25DbGljaz17b25DbG9zZX1cbiAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgIGJhY2tncm91bmQ6ICdub25lJyxcbiAgICAgICAgICAgICAgYm9yZGVyOiAnbm9uZScsXG4gICAgICAgICAgICAgIGNvbG9yOiAnd2hpdGUnLFxuICAgICAgICAgICAgICBjdXJzb3I6ICdwb2ludGVyJyxcbiAgICAgICAgICAgICAgZm9udFNpemU6ICcxOHB4JyxcbiAgICAgICAgICAgICAgb3BhY2l0eTogMC44LFxuICAgICAgICAgICAgICBwYWRkaW5nOiAnNHB4J1xuICAgICAgICAgICAgfX1cbiAgICAgICAgICA+XG4gICAgICAgICAgICDDl1xuICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogTWVzc2FnZXMgKi99XG4gICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgIGZsZXg6IDEsXG4gICAgICAgIHBhZGRpbmc6ICcyMHB4JyxcbiAgICAgICAgb3ZlcmZsb3dZOiAnYXV0bycsXG4gICAgICAgIGRpc3BsYXk6ICdmbGV4JyxcbiAgICAgICAgZmxleERpcmVjdGlvbjogJ2NvbHVtbicsXG4gICAgICAgIGdhcDogJzE2cHgnXG4gICAgICB9fT5cbiAgICAgICAge21lc3NhZ2VzLm1hcCgobWVzc2FnZSkgPT4gKFxuICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgIGtleT17bWVzc2FnZS5pZH1cbiAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgIGRpc3BsYXk6ICdmbGV4JyxcbiAgICAgICAgICAgICAganVzdGlmeUNvbnRlbnQ6IG1lc3NhZ2UudHlwZSA9PT0gJ3VzZXInID8gJ2ZsZXgtZW5kJyA6ICdmbGV4LXN0YXJ0J1xuICAgICAgICAgICAgfX1cbiAgICAgICAgICA+XG4gICAgICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgICAgIG1heFdpZHRoOiAnODAlJyxcbiAgICAgICAgICAgICAgcGFkZGluZzogJzEycHggMTZweCcsXG4gICAgICAgICAgICAgIGJvcmRlclJhZGl1czogbWVzc2FnZS50eXBlID09PSAndXNlcicgPyAnMTZweCAxNnB4IDRweCAxNnB4JyA6ICcxNnB4IDE2cHggMTZweCA0cHgnLFxuICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiBtZXNzYWdlLnR5cGUgPT09ICd1c2VyJyBcbiAgICAgICAgICAgICAgICA/IGBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAke2NvbG9ycy5wcmltYXJ5fSAwJSwgJHtjb2xvcnMucHJpbWFyeUxpZ2h0fSAxMDAlKWBcbiAgICAgICAgICAgICAgICA6IGNvbG9ycy53YXJtR2xvdyArICc0MCcsXG4gICAgICAgICAgICAgIGNvbG9yOiBtZXNzYWdlLnR5cGUgPT09ICd1c2VyJyA/ICd3aGl0ZScgOiBjb2xvcnMudGV4dC5wcmltYXJ5XG4gICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwic2YtcHJvXCIgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICBmb250U2l6ZTogJzE0cHgnLFxuICAgICAgICAgICAgICAgIGxpbmVIZWlnaHQ6ICcxLjQnLFxuICAgICAgICAgICAgICAgIG1hcmdpbjogMCxcbiAgICAgICAgICAgICAgICBmb250V2VpZ2h0OiBtZXNzYWdlLnR5cGUgPT09ICd1c2VyJyA/ICc1MDAnIDogJzQwMCdcbiAgICAgICAgICAgICAgfX0+XG4gICAgICAgICAgICAgICAge21lc3NhZ2UuY29udGVudH1cbiAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAge21lc3NhZ2Uuc3RhdHVzICYmIChcbiAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgICBkaXNwbGF5OiAnZmxleCcsXG4gICAgICAgICAgICAgICAgICBhbGlnbkl0ZW1zOiAnY2VudGVyJyxcbiAgICAgICAgICAgICAgICAgIGdhcDogJzZweCcsXG4gICAgICAgICAgICAgICAgICBtYXJnaW5Ub3A6ICc4cHgnLFxuICAgICAgICAgICAgICAgICAgZm9udFNpemU6ICcxMnB4JyxcbiAgICAgICAgICAgICAgICAgIG9wYWNpdHk6IDAuOFxuICAgICAgICAgICAgICAgIH19PlxuICAgICAgICAgICAgICAgICAge21lc3NhZ2Uuc3RhdHVzID09PSAncG9zdGVkJyAmJiA8Q2hlY2tDaXJjbGUgc2l6ZT17MTJ9IC8+fVxuICAgICAgICAgICAgICAgICAge21lc3NhZ2Uuc3RhdHVzID09PSAnc2NoZWR1bGVkJyAmJiA8Q2xvY2sgc2l6ZT17MTJ9IC8+fVxuICAgICAgICAgICAgICAgICAge21lc3NhZ2Uuc3RhdHVzID09PSAnc2VuZGluZycgJiYgPExvYWRlciBzaXplPXsxMn0gY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluXCIgLz59XG4gICAgICAgICAgICAgICAgICA8c3Bhbj57bWVzc2FnZS5zdGF0dXMgPT09ICdwb3N0ZWQnID8gJ1Bvc3RlZCcgOiBtZXNzYWdlLnN0YXR1c308L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKSl9XG4gICAgICAgIFxuICAgICAgICB7aXNMb2FkaW5nICYmIChcbiAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGRpc3BsYXk6ICdmbGV4JywganVzdGlmeUNvbnRlbnQ6ICdmbGV4LXN0YXJ0JyB9fT5cbiAgICAgICAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgICAgICAgcGFkZGluZzogJzEycHggMTZweCcsXG4gICAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzE2cHggMTZweCAxNnB4IDRweCcsXG4gICAgICAgICAgICAgIGJhY2tncm91bmQ6IGNvbG9ycy53YXJtR2xvdyArICc0MCcsXG4gICAgICAgICAgICAgIGRpc3BsYXk6ICdmbGV4JyxcbiAgICAgICAgICAgICAgYWxpZ25JdGVtczogJ2NlbnRlcicsXG4gICAgICAgICAgICAgIGdhcDogJzhweCdcbiAgICAgICAgICAgIH19PlxuICAgICAgICAgICAgICA8TG9hZGVyIHNpemU9ezE0fSBjbGFzc05hbWU9XCJhbmltYXRlLXNwaW5cIiBjb2xvcj17Y29sb3JzLnByaW1hcnl9IC8+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInNmLXByb1wiIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgZm9udFNpemU6ICcxNHB4JyxcbiAgICAgICAgICAgICAgICBjb2xvcjogY29sb3JzLnRleHQuc2Vjb25kYXJ5XG4gICAgICAgICAgICAgIH19PlxuICAgICAgICAgICAgICAgIEFnZW50IEUgaXMgdGhpbmtpbmcuLi5cbiAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG4gICAgICAgIFxuICAgICAgICA8ZGl2IHJlZj17bWVzc2FnZXNFbmRSZWZ9IC8+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIElucHV0ICovfVxuICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICBwYWRkaW5nOiAnMjBweCcsXG4gICAgICAgIGJvcmRlclRvcDogYDFweCBzb2xpZCAke2NvbG9ycy5ib3JkZXJ9YCxcbiAgICAgICAgYmFja2dyb3VuZDogY29sb3JzLndhcm1HbG93ICsgJzIwJ1xuICAgICAgfX0+XG4gICAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgICBkaXNwbGF5OiAnZmxleCcsXG4gICAgICAgICAgZ2FwOiAnMTJweCcsXG4gICAgICAgICAgYWxpZ25JdGVtczogJ2ZsZXgtZW5kJ1xuICAgICAgICB9fT5cbiAgICAgICAgICA8dGV4dGFyZWFcbiAgICAgICAgICAgIHZhbHVlPXtpbnB1dFZhbHVlfVxuICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRJbnB1dFZhbHVlKGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgIG9uS2V5UHJlc3M9e2hhbmRsZUtleVByZXNzfVxuICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJBc2sgQWdlbnQgRSB0byBzY2hlZHVsZSwgaW1wcm92ZSwgb3IgcG9zdCB5b3VyIGNvbnRlbnQuLi5cIlxuICAgICAgICAgICAgY2xhc3NOYW1lPVwic2YtcHJvXCJcbiAgICAgICAgICAgIHJvd3M9ezJ9XG4gICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICBmbGV4OiAxLFxuICAgICAgICAgICAgICBwYWRkaW5nOiAnMTJweCcsXG4gICAgICAgICAgICAgIGJvcmRlcjogYDFweCBzb2xpZCAke2NvbG9ycy5ib3JkZXJ9YCxcbiAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnMTJweCcsXG4gICAgICAgICAgICAgIGZvbnRTaXplOiAnMTRweCcsXG4gICAgICAgICAgICAgIGJhY2tncm91bmQ6IGNvbG9ycy5zdXJmYWNlLFxuICAgICAgICAgICAgICByZXNpemU6ICdub25lJyxcbiAgICAgICAgICAgICAgb3V0bGluZTogJ25vbmUnXG4gICAgICAgICAgICB9fVxuICAgICAgICAgIC8+XG4gICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgb25DbGljaz17aGFuZGxlU2VuZE1lc3NhZ2V9XG4gICAgICAgICAgICBkaXNhYmxlZD17IWlucHV0VmFsdWUudHJpbSgpIHx8IGlzTG9hZGluZ31cbiAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgIHBhZGRpbmc6ICcxMnB4JyxcbiAgICAgICAgICAgICAgYmFja2dyb3VuZDogYGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICR7Y29sb3JzLnByaW1hcnl9IDAlLCAke2NvbG9ycy5wcmltYXJ5TGlnaHR9IDEwMCUpYCxcbiAgICAgICAgICAgICAgY29sb3I6ICd3aGl0ZScsXG4gICAgICAgICAgICAgIGJvcmRlcjogJ25vbmUnLFxuICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6ICcxMnB4JyxcbiAgICAgICAgICAgICAgY3Vyc29yOiBpbnB1dFZhbHVlLnRyaW0oKSAmJiAhaXNMb2FkaW5nID8gJ3BvaW50ZXInIDogJ25vdC1hbGxvd2VkJyxcbiAgICAgICAgICAgICAgb3BhY2l0eTogaW5wdXRWYWx1ZS50cmltKCkgJiYgIWlzTG9hZGluZyA/IDEgOiAwLjUsXG4gICAgICAgICAgICAgIHRyYW5zaXRpb246ICdhbGwgMC4ycyBlYXNlJ1xuICAgICAgICAgICAgfX1cbiAgICAgICAgICA+XG4gICAgICAgICAgICA8U2VuZCBzaXplPXsxNn0gLz5cbiAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKTtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IEFnZW50RUNoYXQ7XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsInVzZVJlZiIsInVzZUVmZmVjdCIsIkJvdCIsIlNlbmQiLCJMb2FkZXIiLCJDaGVja0NpcmNsZSIsIkNsb2NrIiwiQWdlbnRFQ2hhdCIsImlzT3BlbiIsIm9uQ2xvc2UiLCJjdXJyZW50Q29udGVudCIsIm1lc3NhZ2VzIiwic2V0TWVzc2FnZXMiLCJpZCIsInR5cGUiLCJjb250ZW50IiwidGltZXN0YW1wIiwiRGF0ZSIsImlucHV0VmFsdWUiLCJzZXRJbnB1dFZhbHVlIiwiaXNMb2FkaW5nIiwic2V0SXNMb2FkaW5nIiwibWVzc2FnZXNFbmRSZWYiLCJjb2xvcnMiLCJwcmltYXJ5IiwicHJpbWFyeUxpZ2h0IiwidGV4dCIsInNlY29uZGFyeSIsInRlcnRpYXJ5IiwiYm9yZGVyIiwic3VyZmFjZSIsIndhcm1HbG93IiwiYmFja2dyb3VuZCIsInNjcm9sbFRvQm90dG9tIiwiY3VycmVudCIsInNjcm9sbEludG9WaWV3IiwiYmVoYXZpb3IiLCJzaW11bGF0ZUFnZW50UmVzcG9uc2UiLCJ1c2VyTWVzc2FnZSIsIlByb21pc2UiLCJyZXNvbHZlIiwic2V0VGltZW91dCIsImxvd2VyTWVzc2FnZSIsInRvTG93ZXJDYXNlIiwiaW5jbHVkZXMiLCJoYW5kbGVTZW5kTWVzc2FnZSIsInRyaW0iLCJub3ciLCJ0b1N0cmluZyIsInByZXYiLCJhZ2VudFJlc3BvbnNlIiwiYWdlbnRNZXNzYWdlIiwicG9zdENvbmZpcm1hdGlvbiIsInN0YXR1cyIsInR3ZWV0RGF0YSIsInBvc3RlZCIsImVycm9yIiwiY29uc29sZSIsImhhbmRsZUtleVByZXNzIiwiZSIsImtleSIsInNoaWZ0S2V5IiwicHJldmVudERlZmF1bHQiLCJkaXYiLCJzdHlsZSIsInBvc2l0aW9uIiwiYm90dG9tIiwicmlnaHQiLCJ3aWR0aCIsImhlaWdodCIsImJvcmRlclJhZGl1cyIsImJveFNoYWRvdyIsImRpc3BsYXkiLCJmbGV4RGlyZWN0aW9uIiwiekluZGV4Iiwib3ZlcmZsb3ciLCJwYWRkaW5nIiwiYm9yZGVyQm90dG9tIiwiY29sb3IiLCJhbGlnbkl0ZW1zIiwianVzdGlmeUNvbnRlbnQiLCJnYXAiLCJzaXplIiwiaDMiLCJjbGFzc05hbWUiLCJmb250U2l6ZSIsImZvbnRXZWlnaHQiLCJtYXJnaW4iLCJwIiwib3BhY2l0eSIsImJ1dHRvbiIsIm9uQ2xpY2siLCJjdXJzb3IiLCJmbGV4Iiwib3ZlcmZsb3dZIiwibWFwIiwibWVzc2FnZSIsIm1heFdpZHRoIiwibGluZUhlaWdodCIsIm1hcmdpblRvcCIsInNwYW4iLCJyZWYiLCJib3JkZXJUb3AiLCJ0ZXh0YXJlYSIsInZhbHVlIiwib25DaGFuZ2UiLCJ0YXJnZXQiLCJvbktleVByZXNzIiwicGxhY2Vob2xkZXIiLCJyb3dzIiwicmVzaXplIiwib3V0bGluZSIsImRpc2FibGVkIiwidHJhbnNpdGlvbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./components/AgentEChat.tsx\n");

/***/ }),

/***/ "./components/SidebarLayout.tsx":
/*!**************************************!*\
  !*** ./components/SidebarLayout.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_BarChart3_CreditCard_Crown_HelpCircle_Home_LogOut_MessageCircle_Settings_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,CreditCard,Crown,HelpCircle,Home,LogOut,MessageCircle,Settings,Video!=!lucide-react */ \"__barrel_optimize__?names=BarChart3,CreditCard,Crown,HelpCircle,Home,LogOut,MessageCircle,Settings,Video!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n\n\n\n\n\nconst SidebarLayout = ({ children })=>{\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [hoveredItem, setHoveredItem] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [showAccountMenu, setShowAccountMenu] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const accountMenuRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    // Warm Intelligence Theme - Focused, Intelligent, Warm\n    const colors = {\n        primary: \"#FF6B35\",\n        primaryLight: \"#FF8A65\",\n        primaryDark: \"#E65100\",\n        accent: \"#FFF3E0\",\n        surface: \"#FEFEFE\",\n        surfaceElevated: \"#FFFFFF\",\n        background: \"#FFF8F3\",\n        warmGlow: \"#FFE0B2\",\n        text: {\n            primary: \"#2D1B14\",\n            secondary: \"#5D4037\",\n            tertiary: \"#8D6E63\",\n            muted: \"#BCAAA4\",\n            inverse: \"#FFFFFF\" // White text\n        },\n        border: {\n            light: \"#F5E6D3\",\n            medium: \"#E1C4A0\",\n            primary: \"#FF6B35\" // Primary border\n        },\n        sidebar: {\n            background: \"linear-gradient(135deg, #FF6B35 0%, #FF8A65 50%, #FFB74D 100%)\",\n            backgroundSolid: \"#FF6B35\",\n            text: \"#FFFFFF\",\n            textSecondary: \"rgba(255, 255, 255, 0.9)\",\n            textTertiary: \"rgba(255, 255, 255, 0.7)\",\n            hover: \"rgba(255, 255, 255, 0.15)\",\n            active: \"rgba(255, 255, 255, 0.25)\",\n            border: \"rgba(255, 255, 255, 0.2)\",\n            glow: \"rgba(255, 107, 53, 0.3)\"\n        }\n    };\n    const menuItems = [\n        {\n            href: \"/\",\n            label: \"Briefing Room\",\n            icon: _barrel_optimize_names_BarChart3_CreditCard_Crown_HelpCircle_Home_LogOut_MessageCircle_Settings_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Home,\n            description: \"Mission Control\"\n        },\n        {\n            href: \"/tweet-center\",\n            label: \"Drafting Desk\",\n            icon: _barrel_optimize_names_BarChart3_CreditCard_Crown_HelpCircle_Home_LogOut_MessageCircle_Settings_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.MessageCircle,\n            description: \"AI Writing\"\n        },\n        {\n            href: \"/dashboard\",\n            label: \"Growth Lab\",\n            icon: _barrel_optimize_names_BarChart3_CreditCard_Crown_HelpCircle_Home_LogOut_MessageCircle_Settings_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.BarChart3,\n            description: \"Analytics\"\n        },\n        {\n            href: \"/meeting\",\n            label: \"AI Meetings\",\n            icon: _barrel_optimize_names_BarChart3_CreditCard_Crown_HelpCircle_Home_LogOut_MessageCircle_Settings_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Video,\n            description: \"Video Calls\"\n        }\n    ];\n    const isActive = (href)=>{\n        if (href === \"/\") {\n            return router.pathname === \"/\";\n        }\n        return router.pathname.startsWith(href);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            display: \"flex\",\n            minHeight: \"100vh\",\n            background: `\n        radial-gradient(circle at 20% 20%, ${colors.primary}15 0%, transparent 50%),\n        radial-gradient(circle at 80% 80%, ${colors.primaryLight}10 0%, transparent 50%),\n        linear-gradient(135deg, ${colors.primary} 0%, ${colors.primaryLight} 100%)\n      `,\n            padding: \"20px\",\n            gap: \"20px\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                style: {\n                    width: \"200px\",\n                    background: colors.sidebar.background,\n                    minHeight: \"calc(100vh - 40px)\",\n                    position: \"relative\",\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    borderRadius: \"16px\",\n                    boxShadow: `\n          0 20px 60px ${colors.sidebar.glow},\n          0 8px 32px rgba(0, 0, 0, 0.15),\n          inset 0 1px 0 rgba(255, 255, 255, 0.2)\n        `,\n                    overflow: \"visible\",\n                    backdropFilter: \"blur(10px)\",\n                    border: `1px solid rgba(255, 255, 255, 0.1)`\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: \"24px\",\n                            borderBottom: `1px solid ${colors.sidebar.border}`,\n                            display: \"flex\",\n                            justifyContent: \"center\",\n                            alignItems: \"center\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            style: {\n                                fontSize: \"32px\",\n                                color: colors.sidebar.text,\n                                fontWeight: \"400\",\n                                fontFamily: \"Georgia, serif\",\n                                fontStyle: \"italic\",\n                                textShadow: \"0 2px 8px rgba(0, 0, 0, 0.3)\",\n                                letterSpacing: \"-1px\"\n                            },\n                            children: \"ℰ\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        style: {\n                            flex: 1,\n                            padding: \"20px 0\",\n                            overflow: \"visible\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                padding: \"0 16px\"\n                            },\n                            children: menuItems.map((item, index)=>{\n                                const active = isActive(item.href);\n                                const hovered = hoveredItem === item.href;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        marginBottom: \"6px\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: item.href,\n                                        style: {\n                                            textDecoration: \"none\"\n                                        },\n                                        onMouseEnter: ()=>setHoveredItem(item.href),\n                                        onMouseLeave: ()=>setHoveredItem(null),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                padding: \"12px 16px\",\n                                                borderRadius: \"12px\",\n                                                transition: \"all 0.2s ease\",\n                                                cursor: \"pointer\",\n                                                position: \"relative\",\n                                                background: active ? `linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.1) 100%)` : hovered ? `linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 100%)` : \"transparent\",\n                                                backdropFilter: active || hovered ? \"blur(10px)\" : \"none\",\n                                                border: active ? \"1px solid rgba(255, 255, 255, 0.3)\" : \"1px solid transparent\",\n                                                boxShadow: active ? `0 4px 16px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.4)` : \"none\"\n                                            },\n                                            children: [\n                                                active && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        position: \"absolute\",\n                                                        left: \"-1px\",\n                                                        top: \"50%\",\n                                                        transform: \"translateY(-50%)\",\n                                                        width: \"3px\",\n                                                        height: \"20px\",\n                                                        background: `linear-gradient(180deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.6) 100%)`,\n                                                        borderRadius: \"0 6px 6px 0\",\n                                                        boxShadow: \"0 0 8px rgba(255, 255, 255, 0.5)\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 175,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        width: \"24px\",\n                                                        height: \"24px\",\n                                                        display: \"flex\",\n                                                        alignItems: \"center\",\n                                                        justifyContent: \"center\",\n                                                        marginRight: \"12px\",\n                                                        borderRadius: \"6px\",\n                                                        background: active ? `linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.1) 100%)` : \"transparent\"\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                        size: 16,\n                                                        color: colors.sidebar.text,\n                                                        style: {\n                                                            filter: active ? \"drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2))\" : \"none\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                        lineNumber: 201,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 189,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"15px\",\n                                                        fontWeight: active ? \"600\" : \"500\",\n                                                        color: colors.sidebar.text,\n                                                        letterSpacing: \"-0.3px\",\n                                                        textShadow: active ? \"0 1px 2px rgba(0, 0, 0, 0.1)\" : \"none\"\n                                                    },\n                                                    children: item.label\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 211,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, item.href, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 17\n                                }, undefined);\n                            })\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: accountMenuRef,\n                        style: {\n                            padding: \"16px 12px\",\n                            borderTop: `1px solid ${colors.sidebar.border}`,\n                            marginTop: \"auto\",\n                            background: `radial-gradient(circle at center bottom, rgba(255, 255, 255, 0.1) 0%, transparent 70%)`,\n                            position: \"relative\"\n                        },\n                        onMouseEnter: ()=>setShowAccountMenu(true),\n                        onMouseLeave: ()=>setShowAccountMenu(false),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"8px\",\n                                    padding: \"8px 12px\",\n                                    background: `linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.05) 100%)`,\n                                    borderRadius: \"8px\",\n                                    cursor: \"pointer\",\n                                    transition: \"all 0.15s ease\",\n                                    backdropFilter: \"blur(10px)\",\n                                    border: \"1px solid rgba(255, 255, 255, 0.2)\",\n                                    boxShadow: `0 2px 8px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.3)`\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            width: \"24px\",\n                                            height: \"24px\",\n                                            background: `linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.1) 100%)`,\n                                            borderRadius: \"6px\",\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            justifyContent: \"center\",\n                                            position: \"relative\",\n                                            backdropFilter: \"blur(10px)\",\n                                            border: \"1px solid rgba(255, 255, 255, 0.2)\",\n                                            flexShrink: 0\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    color: colors.sidebar.text,\n                                                    fontSize: \"11px\",\n                                                    fontWeight: \"600\",\n                                                    textShadow: \"0 1px 2px rgba(0, 0, 0, 0.1)\"\n                                                },\n                                                children: \"A\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                lineNumber: 271,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    position: \"absolute\",\n                                                    bottom: \"-1px\",\n                                                    right: \"-1px\",\n                                                    width: \"6px\",\n                                                    height: \"6px\",\n                                                    borderRadius: \"50%\",\n                                                    background: `radial-gradient(circle, #00E676 0%, #00C853 100%)`,\n                                                    border: \"1px solid rgba(255, 255, 255, 0.9)\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            flex: 1,\n                                            minWidth: 0\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: \"12px\",\n                                                    fontWeight: \"600\",\n                                                    color: colors.sidebar.text,\n                                                    lineHeight: \"1.2\",\n                                                    marginBottom: \"1px\",\n                                                    textShadow: \"0 1px 2px rgba(0, 0, 0, 0.1)\",\n                                                    overflow: \"hidden\",\n                                                    textOverflow: \"ellipsis\",\n                                                    whiteSpace: \"nowrap\"\n                                                },\n                                                children: \"Alex Chen\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                lineNumber: 294,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: \"10px\",\n                                                    color: colors.sidebar.textTertiary,\n                                                    lineHeight: \"1.2\",\n                                                    fontWeight: \"500\"\n                                                },\n                                                children: \"AI Manager\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                        lineNumber: 293,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            width: \"12px\",\n                                            height: \"12px\",\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            justifyContent: \"center\",\n                                            borderRadius: \"3px\",\n                                            background: `linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.05) 100%)`,\n                                            flexShrink: 0\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                fontSize: \"8px\",\n                                                color: colors.sidebar.textSecondary,\n                                                transform: showAccountMenu ? \"rotate(180deg)\" : \"rotate(0deg)\",\n                                                transition: \"transform 0.2s ease\"\n                                            },\n                                            children: \"⌄\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                        lineNumber: 318,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 11\n                            }, undefined),\n                            showAccountMenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    position: \"absolute\",\n                                    left: \"100%\",\n                                    bottom: \"0\",\n                                    marginLeft: \"12px\",\n                                    width: \"320px\",\n                                    background: `\n                linear-gradient(135deg,\n                  rgba(255, 255, 255, 0.98) 0%,\n                  rgba(255, 255, 255, 0.95) 100%\n                )\n              `,\n                                    borderRadius: \"12px\",\n                                    boxShadow: `\n                0 8px 32px rgba(0, 0, 0, 0.12),\n                0 4px 16px rgba(0, 0, 0, 0.08),\n                0 2px 8px rgba(255, 107, 53, 0.15),\n                inset 0 1px 0 rgba(255, 255, 255, 0.9)\n              `,\n                                    border: `1px solid rgba(255, 107, 53, 0.15)`,\n                                    overflow: \"hidden\",\n                                    zIndex: 9999,\n                                    backdropFilter: \"blur(16px)\",\n                                    opacity: showAccountMenu ? 1 : 0,\n                                    transform: showAccountMenu ? \"translateX(0)\" : \"translateX(-8px)\",\n                                    transition: \"all 0.15s ease\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            padding: \"12px 16px\",\n                                            borderBottom: `1px solid rgba(255, 107, 53, 0.1)`,\n                                            background: `linear-gradient(135deg, rgba(255, 107, 53, 0.08) 0%, rgba(255, 138, 101, 0.04) 100%)`\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                gap: \"8px\"\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        width: \"28px\",\n                                                        height: \"28px\",\n                                                        background: `linear-gradient(135deg, ${colors.primary} 0%, ${colors.primaryLight} 100%)`,\n                                                        borderRadius: \"6px\",\n                                                        display: \"flex\",\n                                                        alignItems: \"center\",\n                                                        justifyContent: \"center\",\n                                                        boxShadow: \"0 2px 8px rgba(255, 107, 53, 0.25)\",\n                                                        position: \"relative\"\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            style: {\n                                                                color: \"white\",\n                                                                fontSize: \"12px\",\n                                                                fontWeight: \"600\",\n                                                                textShadow: \"0 1px 2px rgba(0, 0, 0, 0.2)\"\n                                                            },\n                                                            children: \"A\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                            lineNumber: 390,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                position: \"absolute\",\n                                                                bottom: \"-1px\",\n                                                                right: \"-1px\",\n                                                                width: \"8px\",\n                                                                height: \"8px\",\n                                                                borderRadius: \"50%\",\n                                                                background: `linear-gradient(135deg, #00E676 0%, #00C853 100%)`,\n                                                                border: \"1.5px solid white\",\n                                                                boxShadow: \"0 0 4px rgba(0, 230, 118, 0.4)\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                            lineNumber: 398,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 379,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                fontSize: \"13px\",\n                                                                fontWeight: \"600\",\n                                                                color: colors.text.primary,\n                                                                lineHeight: \"1.2\"\n                                                            },\n                                                            children: \"Alex Chen\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                            lineNumber: 411,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                fontSize: \"11px\",\n                                                                color: colors.text.secondary,\n                                                                fontWeight: \"500\"\n                                                            },\n                                                            children: \"Pro Member\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                            lineNumber: 419,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 410,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 374,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                        lineNumber: 369,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"grid\",\n                                            gridTemplateColumns: \"repeat(2, 1fr)\",\n                                            gap: \"1px\",\n                                            background: `rgba(255, 107, 53, 0.06)`\n                                        },\n                                        children: [\n                                            {\n                                                label: \"Settings\",\n                                                icon: _barrel_optimize_names_BarChart3_CreditCard_Crown_HelpCircle_Home_LogOut_MessageCircle_Settings_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Settings,\n                                                href: \"/settings\"\n                                            },\n                                            {\n                                                label: \"Subscription\",\n                                                icon: _barrel_optimize_names_BarChart3_CreditCard_Crown_HelpCircle_Home_LogOut_MessageCircle_Settings_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Crown\n                                            },\n                                            {\n                                                label: \"Billing\",\n                                                icon: _barrel_optimize_names_BarChart3_CreditCard_Crown_HelpCircle_Home_LogOut_MessageCircle_Settings_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.CreditCard\n                                            },\n                                            {\n                                                label: \"Help\",\n                                                icon: _barrel_optimize_names_BarChart3_CreditCard_Crown_HelpCircle_Home_LogOut_MessageCircle_Settings_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.HelpCircle\n                                            }\n                                        ].map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    padding: \"12px\",\n                                                    cursor: \"pointer\",\n                                                    transition: \"all 0.1s ease\",\n                                                    display: \"flex\",\n                                                    flexDirection: \"column\",\n                                                    alignItems: \"center\",\n                                                    gap: \"6px\",\n                                                    background: \"rgba(255, 255, 255, 0.8)\",\n                                                    position: \"relative\"\n                                                },\n                                                onMouseEnter: (e)=>{\n                                                    e.currentTarget.style.background = `rgba(255, 107, 53, 0.08)`;\n                                                },\n                                                onMouseLeave: (e)=>{\n                                                    e.currentTarget.style.background = \"rgba(255, 255, 255, 0.8)\";\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            width: \"28px\",\n                                                            height: \"28px\",\n                                                            borderRadius: \"6px\",\n                                                            background: `linear-gradient(135deg, rgba(255, 107, 53, 0.12) 0%, rgba(255, 138, 101, 0.06) 100%)`,\n                                                            display: \"flex\",\n                                                            alignItems: \"center\",\n                                                            justifyContent: \"center\",\n                                                            border: `1px solid rgba(255, 107, 53, 0.15)`\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                            size: 14,\n                                                            color: colors.primary,\n                                                            style: {\n                                                                filter: \"drop-shadow(0 1px 2px rgba(255, 107, 53, 0.2))\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                            lineNumber: 473,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                        lineNumber: 463,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            fontSize: \"11px\",\n                                                            fontWeight: \"500\",\n                                                            color: colors.text.primary,\n                                                            textAlign: \"center\"\n                                                        },\n                                                        children: item.label\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                        lineNumber: 481,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                lineNumber: 443,\n                                                columnNumber: 19\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                        lineNumber: 431,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            padding: \"8px 12px\",\n                                            borderTop: `1px solid rgba(255, 107, 53, 0.1)`\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                padding: \"8px 12px\",\n                                                cursor: \"pointer\",\n                                                transition: \"all 0.1s ease\",\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                gap: \"8px\",\n                                                borderRadius: \"6px\",\n                                                background: \"transparent\"\n                                            },\n                                            onMouseEnter: (e)=>{\n                                                e.currentTarget.style.background = `rgba(231, 76, 60, 0.08)`;\n                                            },\n                                            onMouseLeave: (e)=>{\n                                                e.currentTarget.style.background = \"transparent\";\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        width: \"20px\",\n                                                        height: \"20px\",\n                                                        borderRadius: \"4px\",\n                                                        background: `linear-gradient(135deg, rgba(231, 76, 60, 0.12) 0%, rgba(231, 76, 60, 0.06) 100%)`,\n                                                        display: \"flex\",\n                                                        alignItems: \"center\",\n                                                        justifyContent: \"center\",\n                                                        border: `1px solid rgba(231, 76, 60, 0.2)`\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_CreditCard_Crown_HelpCircle_Home_LogOut_MessageCircle_Settings_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.LogOut, {\n                                                        size: 11,\n                                                        color: \"#e74c3c\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                        lineNumber: 526,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 516,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"12px\",\n                                                        fontWeight: \"500\",\n                                                        color: \"#e74c3c\"\n                                                    },\n                                                    children: \"Sign Out\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 531,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 498,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                        lineNumber: 494,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 341,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                lineNumber: 98,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                style: {\n                    flexGrow: 1,\n                    position: \"relative\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        backgroundColor: colors.surfaceElevated,\n                        borderRadius: \"20px\",\n                        minHeight: \"calc(100vh - 40px)\",\n                        boxShadow: `\n            0 32px 80px rgba(0, 0, 0, 0.12),\n            0 8px 32px rgba(0, 0, 0, 0.08),\n            inset 0 1px 0 rgba(255, 255, 255, 0.9),\n            0 0 0 1px rgba(255, 107, 53, 0.1)\n          `,\n                        overflow: \"hidden\",\n                        position: \"relative\",\n                        backdropFilter: \"blur(20px)\",\n                        border: \"1px solid rgba(255, 255, 255, 0.2)\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                position: \"absolute\",\n                                top: 0,\n                                left: 0,\n                                right: 0,\n                                height: \"200px\",\n                                background: `\n              radial-gradient(ellipse at top, ${colors.warmGlow}20 0%, transparent 70%)\n            `,\n                                pointerEvents: \"none\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                            lineNumber: 566,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                position: \"relative\",\n                                zIndex: 1,\n                                height: \"100%\"\n                            },\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                            lineNumber: 579,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                    lineNumber: 550,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                lineNumber: 546,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n        lineNumber: 86,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SidebarLayout);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/SidebarLayout.tsx\n");

/***/ }),

/***/ "./pages/_app.tsx":
/*!************************!*\
  !*** ./pages/_app.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/globals.css */ \"./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n\n // Global styles\nfunction MyApp({ Component, pageProps }) {\n    // Use the layout defined at the page level, if available\n    const getLayout = Component.getLayout || ((page)=>page);\n    return getLayout(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n        ...pageProps\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/_app.tsx\",\n        lineNumber: 18,\n        columnNumber: 20\n    }, this));\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MyApp);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy9fYXBwLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBK0IsQ0FBQyxnQkFBZ0I7QUFhaEQsU0FBU0EsTUFBTSxFQUFFQyxTQUFTLEVBQUVDLFNBQVMsRUFBc0I7SUFDekQseURBQXlEO0lBQ3pELE1BQU1DLFlBQVlGLFVBQVVFLFNBQVMsSUFBSyxFQUFDQyxPQUFTQSxJQUFHO0lBRXZELE9BQU9ELHdCQUFVLDhEQUFDRjtRQUFXLEdBQUdDLFNBQVM7Ozs7OztBQUMzQztBQUVBLGlFQUFlRixLQUFLQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZXhpZS1haS8uL3BhZ2VzL19hcHAudHN4PzJmYmUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICcuLi9zdHlsZXMvZ2xvYmFscy5jc3MnOyAvLyBHbG9iYWwgc3R5bGVzXG5pbXBvcnQgdHlwZSB7IEFwcFByb3BzIH0gZnJvbSAnbmV4dC9hcHAnO1xuaW1wb3J0IHR5cGUgeyBSZWFjdEVsZW1lbnQsIFJlYWN0Tm9kZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB0eXBlIHsgTmV4dFBhZ2UgfSBmcm9tICduZXh0JztcblxuZXhwb3J0IHR5cGUgTmV4dFBhZ2VXaXRoTGF5b3V0ID0gTmV4dFBhZ2UgJiB7XG4gIGdldExheW91dD86IChwYWdlOiBSZWFjdEVsZW1lbnQpID0+IFJlYWN0Tm9kZTtcbn07XG5cbmV4cG9ydCB0eXBlIEFwcFByb3BzV2l0aExheW91dCA9IEFwcFByb3BzICYge1xuICBDb21wb25lbnQ6IE5leHRQYWdlV2l0aExheW91dDtcbn07XG5cbmZ1bmN0aW9uIE15QXBwKHsgQ29tcG9uZW50LCBwYWdlUHJvcHMgfTogQXBwUHJvcHNXaXRoTGF5b3V0KSB7XG4gIC8vIFVzZSB0aGUgbGF5b3V0IGRlZmluZWQgYXQgdGhlIHBhZ2UgbGV2ZWwsIGlmIGF2YWlsYWJsZVxuICBjb25zdCBnZXRMYXlvdXQgPSBDb21wb25lbnQuZ2V0TGF5b3V0IHx8ICgocGFnZSkgPT4gcGFnZSk7XG5cbiAgcmV0dXJuIGdldExheW91dCg8Q29tcG9uZW50IHsuLi5wYWdlUHJvcHN9IC8+KTtcbn1cblxuZXhwb3J0IGRlZmF1bHQgTXlBcHA7Il0sIm5hbWVzIjpbIk15QXBwIiwiQ29tcG9uZW50IiwicGFnZVByb3BzIiwiZ2V0TGF5b3V0IiwicGFnZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./pages/_app.tsx\n");

/***/ }),

/***/ "./pages/tweet-center.tsx":
/*!********************************!*\
  !*** ./pages/tweet-center.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_SidebarLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/SidebarLayout */ \"./components/SidebarLayout.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlignCenter_AlignLeft_Bot_Sparkles_Type_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlignCenter,AlignLeft,Bot,Sparkles,Type!=!lucide-react */ \"__barrel_optimize__?names=AlignCenter,AlignLeft,Bot,Sparkles,Type!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _components_AgentEChat__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/AgentEChat */ \"./components/AgentEChat.tsx\");\n// pages/tweet-center.tsx\n\n\n\n\n\nconst TweetCenterPage = ()=>{\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [aiSuggestion, setAiSuggestion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showSuggestion, setShowSuggestion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [aiEnabled, setAiEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [formatMode, setFormatMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"single\");\n    const [agentEOpen, setAgentEOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const colors = {\n        primary: \"#FF6B35\",\n        primaryLight: \"#FF8A65\",\n        text: {\n            primary: \"#2D1B14\",\n            secondary: \"#5D4037\",\n            tertiary: \"#8D6E63\"\n        },\n        border: \"#F5E6D3\",\n        borderHover: \"#E8D5C4\",\n        surface: \"#FFFFFF\",\n        surfaceHover: \"#F9F7F4\",\n        warmGlow: \"#FFE0B2\",\n        paper: \"#FEFEFE\"\n    };\n    // Intelligent AI prediction based on content context\n    const generateContextualSuggestion = (text)=>{\n        const lowerText = text.toLowerCase();\n        // Trading/Finance context\n        if (lowerText.includes(\"trading\") || lowerText.includes(\"stocks\") || lowerText.includes(\"crypto\")) {\n            return \" - risk management is everything. Never trade with money you can't afford to lose.\";\n        }\n        // AI/Tech context\n        if (lowerText.includes(\"ai\") || lowerText.includes(\"artificial intelligence\") || lowerText.includes(\"machine learning\")) {\n            return \" is transforming how we work. The key is learning to collaborate with AI, not compete against it.\";\n        }\n        // Productivity context\n        if (lowerText.includes(\"productivity\") || lowerText.includes(\"workflow\") || lowerText.includes(\"efficiency\")) {\n            return \": 1) Single-task focus 2) Time blocking 3) Automate repetitive work. Small changes, big results.\";\n        }\n        // Building/Entrepreneurship context\n        if (lowerText.includes(\"building\") || lowerText.includes(\"startup\") || lowerText.includes(\"business\")) {\n            return \" in public. Share your journey, failures, and wins. Your audience wants authenticity, not perfection.\";\n        }\n        // Learning/Growth context\n        if (lowerText.includes(\"learning\") || lowerText.includes(\"skill\") || lowerText.includes(\"growth\")) {\n            return \" - the best investment you can make is in yourself. Consistency beats intensity every time.\";\n        }\n        // Default contextual suggestions\n        const endings = [\n            \" - here's what I learned from 5 years of experience.\",\n            \". The biggest mistake I see people make is...\",\n            \". Here are 3 things that changed everything for me:\",\n            \" - and it completely shifted my perspective.\",\n            \". If I started over today, I'd focus on this first.\"\n        ];\n        return endings[Math.floor(Math.random() * endings.length)];\n    };\n    // AI prediction with context awareness\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (content.length > 15 && aiEnabled) {\n            const timer = setTimeout(()=>{\n                const suggestion = generateContextualSuggestion(content);\n                setAiSuggestion(suggestion);\n                setShowSuggestion(true);\n            }, 800);\n            return ()=>clearTimeout(timer);\n        } else {\n            setShowSuggestion(false);\n        }\n    }, [\n        content,\n        aiEnabled\n    ]);\n    const handleTabPress = (e)=>{\n        if (e.key === \"Tab\" && showSuggestion) {\n            e.preventDefault();\n            setContent(content + aiSuggestion);\n            setShowSuggestion(false);\n            setAiSuggestion(\"\");\n        }\n    };\n    const autoFormat = ()=>{\n        if (content.length > 280) {\n            // Convert to thread format\n            const sentences = content.split(\". \");\n            const threads = [];\n            let currentThread = \"\";\n            sentences.forEach((sentence)=>{\n                if ((currentThread + sentence + \". \").length <= 280) {\n                    currentThread += sentence + \". \";\n                } else {\n                    if (currentThread) threads.push(currentThread.trim());\n                    currentThread = sentence + \". \";\n                }\n            });\n            if (currentThread) threads.push(currentThread.trim());\n            setContent(threads.join(\"\\n\\n\"));\n            setFormatMode(\"thread\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"40px 60px\",\n            height: \"100vh\",\n            overflow: \"auto\",\n            background: colors.paper,\n            display: \"flex\",\n            flexDirection: \"column\",\n            alignItems: \"center\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    width: \"100%\",\n                    maxWidth: \"800px\",\n                    marginBottom: \"40px\",\n                    textAlign: \"center\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        style: {\n                            color: colors.text.primary,\n                            margin: 0,\n                            fontSize: \"32px\",\n                            fontWeight: \"300\",\n                            letterSpacing: \"-1px\",\n                            marginBottom: \"12px\",\n                            fontFamily: \"Georgia, serif\"\n                        },\n                        children: \"Drafting Desk\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"flex\",\n                            justifyContent: \"center\",\n                            alignItems: \"center\",\n                            gap: \"16px\",\n                            marginTop: \"24px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"8px\",\n                                    padding: \"6px 12px\",\n                                    background: aiEnabled ? `${colors.primary}15` : colors.surface,\n                                    borderRadius: \"20px\",\n                                    border: `1px solid ${aiEnabled ? colors.primary : colors.border}`,\n                                    cursor: \"pointer\",\n                                    transition: \"all 0.2s ease\"\n                                },\n                                onClick: ()=>setAiEnabled(!aiEnabled),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_Bot_Sparkles_Type_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Sparkles, {\n                                        size: 14,\n                                        color: aiEnabled ? colors.primary : colors.text.tertiary\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        style: {\n                                            fontSize: \"13px\",\n                                            fontWeight: \"500\",\n                                            color: aiEnabled ? colors.primary : colors.text.tertiary\n                                        },\n                                        children: \"AI Assistant\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: autoFormat,\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"6px\",\n                                    padding: \"6px 12px\",\n                                    background: colors.surface,\n                                    border: `1px solid ${colors.border}`,\n                                    borderRadius: \"20px\",\n                                    fontSize: \"13px\",\n                                    fontWeight: \"500\",\n                                    color: colors.text.secondary,\n                                    cursor: \"pointer\",\n                                    transition: \"all 0.2s ease\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_Bot_Sparkles_Type_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Type, {\n                                        size: 14\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Auto Format\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"6px\",\n                                    padding: \"6px 12px\",\n                                    background: formatMode === \"thread\" ? `${colors.primary}15` : colors.surface,\n                                    borderRadius: \"20px\",\n                                    border: `1px solid ${formatMode === \"thread\" ? colors.primary : colors.border}`\n                                },\n                                children: [\n                                    formatMode === \"thread\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_Bot_Sparkles_Type_lucide_react__WEBPACK_IMPORTED_MODULE_4__.AlignLeft, {\n                                        size: 14,\n                                        color: colors.primary\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 40\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_Bot_Sparkles_Type_lucide_react__WEBPACK_IMPORTED_MODULE_4__.AlignCenter, {\n                                        size: 14,\n                                        color: colors.text.tertiary\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 89\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        style: {\n                                            fontSize: \"13px\",\n                                            fontWeight: \"500\",\n                                            color: formatMode === \"thread\" ? colors.primary : colors.text.tertiary\n                                        },\n                                        children: formatMode === \"thread\" ? \"Thread\" : \"Single\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                lineNumber: 131,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    width: \"100%\",\n                    maxWidth: \"900px\",\n                    background: \"transparent\",\n                    position: \"relative\",\n                    minHeight: \"500px\",\n                    display: \"flex\",\n                    flexDirection: \"column\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            flex: 1,\n                            position: \"relative\",\n                            padding: \"40px 60px\",\n                            minHeight: \"450px\"\n                        },\n                        children: [\n                            showSuggestion && aiEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    position: \"absolute\",\n                                    top: \"40px\",\n                                    left: \"60px\",\n                                    right: \"60px\",\n                                    bottom: \"40px\",\n                                    pointerEvents: \"none\",\n                                    zIndex: 1,\n                                    overflow: \"hidden\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"sf-pro\",\n                                    style: {\n                                        fontSize: \"20px\",\n                                        lineHeight: \"1.7\",\n                                        color: \"transparent\",\n                                        whiteSpace: \"pre-wrap\",\n                                        wordWrap: \"break-word\",\n                                        letterSpacing: \"0.3px\",\n                                        fontWeight: \"400\"\n                                    },\n                                    children: [\n                                        content,\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                color: \"#9CA3AF\",\n                                                opacity: 0.6,\n                                                fontStyle: \"normal\"\n                                            },\n                                            children: aiSuggestion\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                    lineNumber: 255,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                ref: textareaRef,\n                                value: content,\n                                onChange: (e)=>setContent(e.target.value),\n                                onKeyDown: handleTabPress,\n                                placeholder: aiEnabled ? \"Start writing and I'll help you continue...\" : \"What's on your mind?\",\n                                className: \"sf-pro\",\n                                style: {\n                                    width: \"100%\",\n                                    height: \"100%\",\n                                    minHeight: \"400px\",\n                                    padding: \"0\",\n                                    border: \"none\",\n                                    background: \"transparent\",\n                                    fontSize: \"20px\",\n                                    lineHeight: \"1.7\",\n                                    color: colors.text.primary,\n                                    resize: \"none\",\n                                    outline: \"none\",\n                                    letterSpacing: \"0.3px\",\n                                    position: \"relative\",\n                                    zIndex: 2,\n                                    fontWeight: \"400\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 237,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"flex\",\n                            justifyContent: \"space-between\",\n                            alignItems: \"center\",\n                            padding: \"0 60px 40px\",\n                            marginTop: \"20px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"sf-pro\",\n                                style: {\n                                    fontSize: \"14px\",\n                                    color: colors.text.secondary,\n                                    fontWeight: \"400\",\n                                    opacity: 0.7\n                                },\n                                children: [\n                                    content.length,\n                                    \" characters\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    gap: \"12px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setAgentEOpen(true),\n                                        className: \"sf-pro\",\n                                        style: {\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            gap: \"8px\",\n                                            padding: \"12px 20px\",\n                                            background: `linear-gradient(135deg, rgba(138, 43, 226, 0.1) 0%, rgba(75, 0, 130, 0.1) 100%)`,\n                                            border: `1px solid rgba(138, 43, 226, 0.3)`,\n                                            borderRadius: \"10px\",\n                                            color: \"#8A2BE2\",\n                                            fontSize: \"14px\",\n                                            fontWeight: \"600\",\n                                            cursor: \"pointer\",\n                                            transition: \"all 0.2s ease\"\n                                        },\n                                        onMouseEnter: (e)=>{\n                                            const target = e.target;\n                                            target.style.background = `linear-gradient(135deg, rgba(138, 43, 226, 0.15) 0%, rgba(75, 0, 130, 0.15) 100%)`;\n                                            target.style.transform = \"translateY(-1px)\";\n                                        },\n                                        onMouseLeave: (e)=>{\n                                            const target = e.target;\n                                            target.style.background = `linear-gradient(135deg, rgba(138, 43, 226, 0.1) 0%, rgba(75, 0, 130, 0.1) 100%)`;\n                                            target.style.transform = \"translateY(0)\";\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_Bot_Sparkles_Type_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Bot, {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                lineNumber: 350,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"Agent E\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setContent(\"\"),\n                                        className: \"sf-pro\",\n                                        style: {\n                                            padding: \"12px 24px\",\n                                            background: \"transparent\",\n                                            border: `1px solid ${colors.border}`,\n                                            borderRadius: \"10px\",\n                                            color: colors.text.secondary,\n                                            fontSize: \"14px\",\n                                            fontWeight: \"500\",\n                                            cursor: \"pointer\",\n                                            transition: \"all 0.2s ease\"\n                                        },\n                                        onMouseEnter: (e)=>{\n                                            const target = e.target;\n                                            target.style.background = colors.surfaceHover;\n                                            target.style.borderColor = colors.borderHover;\n                                        },\n                                        onMouseLeave: (e)=>{\n                                            const target = e.target;\n                                            target.style.background = \"transparent\";\n                                            target.style.borderColor = colors.border;\n                                        },\n                                        children: \"Save Draft\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 354,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            // Publish logic here\n                                            console.log(\"Publishing:\", content);\n                                        },\n                                        className: \"sf-pro\",\n                                        style: {\n                                            padding: \"12px 28px\",\n                                            background: `linear-gradient(135deg, ${colors.primary} 0%, ${colors.primaryLight} 100%)`,\n                                            border: \"none\",\n                                            borderRadius: \"10px\",\n                                            color: \"white\",\n                                            fontSize: \"14px\",\n                                            fontWeight: \"600\",\n                                            cursor: \"pointer\",\n                                            boxShadow: `0 4px 12px ${colors.primary}30`,\n                                            transition: \"all 0.2s ease\"\n                                        },\n                                        onMouseEnter: (e)=>{\n                                            const target = e.target;\n                                            target.style.transform = \"translateY(-1px)\";\n                                            target.style.boxShadow = `0 6px 16px ${colors.primary}40`;\n                                        },\n                                        onMouseLeave: (e)=>{\n                                            const target = e.target;\n                                            target.style.transform = \"translateY(0)\";\n                                            target.style.boxShadow = `0 4px 12px ${colors.primary}30`;\n                                        },\n                                        children: \"Publish\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 382,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 321,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 305,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                lineNumber: 226,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AgentEChat__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                isOpen: agentEOpen,\n                onClose: ()=>setAgentEOpen(false),\n                currentContent: content\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                lineNumber: 418,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n        lineNumber: 121,\n        columnNumber: 5\n    }, undefined);\n};\nTweetCenterPage.getLayout = function getLayout(page) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SidebarLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: page\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n        lineNumber: 429,\n        columnNumber: 5\n    }, this);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TweetCenterPage);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/tweet-center.tsx\n");

/***/ }),

/***/ "./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/lucide-react"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Ftweet-center&preferredRegion=&absolutePagePath=.%2Fpages%2Ftweet-center.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();