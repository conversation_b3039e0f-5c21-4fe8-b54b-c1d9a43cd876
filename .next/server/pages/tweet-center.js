/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/tweet-center";
exports.ids = ["pages/tweet-center"];
exports.modules = {

/***/ "__barrel_optimize__?names=BarChart3,CreditCard,Crown,HelpCircle,Home,LogOut,MessageCircle,Settings,Video!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!***********************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=BarChart3,CreditCard,Crown,HelpCircle,Home,LogOut,MessageCircle,Settings,Video!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \***********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BarChart3: () => (/* reexport safe */ _icons_bar_chart_3_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   CreditCard: () => (/* reexport safe */ _icons_credit_card_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   Crown: () => (/* reexport safe */ _icons_crown_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   HelpCircle: () => (/* reexport safe */ _icons_circle_help_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   Home: () => (/* reexport safe */ _icons_house_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   LogOut: () => (/* reexport safe */ _icons_log_out_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]),\n/* harmony export */   MessageCircle: () => (/* reexport safe */ _icons_message_circle_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"]),\n/* harmony export */   Settings: () => (/* reexport safe */ _icons_settings_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"]),\n/* harmony export */   Video: () => (/* reexport safe */ _icons_video_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_bar_chart_3_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/bar-chart-3.js */ \"./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _icons_credit_card_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/credit-card.js */ \"./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _icons_crown_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/crown.js */ \"./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _icons_circle_help_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/circle-help.js */ \"./node_modules/lucide-react/dist/esm/icons/circle-help.js\");\n/* harmony import */ var _icons_house_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./icons/house.js */ \"./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _icons_log_out_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./icons/log-out.js */ \"./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _icons_message_circle_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./icons/message-circle.js */ \"./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _icons_settings_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./icons/settings.js */ \"./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _icons_video_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./icons/video.js */ \"./node_modules/lucide-react/dist/esm/icons/video.js\");\n\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1CYXJDaGFydDMsQ3JlZGl0Q2FyZCxDcm93bixIZWxwQ2lyY2xlLEhvbWUsTG9nT3V0LE1lc3NhZ2VDaXJjbGUsU2V0dGluZ3MsVmlkZW8hPSEuL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vbHVjaWRlLXJlYWN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFDNkQ7QUFDQztBQUNYO0FBQ1c7QUFDWjtBQUNJO0FBQ2M7QUFDWCIsInNvdXJjZXMiOlsid2VicGFjazovL2V4aWUtYWkvLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2x1Y2lkZS1yZWFjdC5qcz8zNjAzIl0sInNvdXJjZXNDb250ZW50IjpbIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBCYXJDaGFydDMgfSBmcm9tIFwiLi9pY29ucy9iYXItY2hhcnQtMy5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIENyZWRpdENhcmQgfSBmcm9tIFwiLi9pY29ucy9jcmVkaXQtY2FyZC5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIENyb3duIH0gZnJvbSBcIi4vaWNvbnMvY3Jvd24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBIZWxwQ2lyY2xlIH0gZnJvbSBcIi4vaWNvbnMvY2lyY2xlLWhlbHAuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBIb21lIH0gZnJvbSBcIi4vaWNvbnMvaG91c2UuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBMb2dPdXQgfSBmcm9tIFwiLi9pY29ucy9sb2ctb3V0LmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgTWVzc2FnZUNpcmNsZSB9IGZyb20gXCIuL2ljb25zL21lc3NhZ2UtY2lyY2xlLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgU2V0dGluZ3MgfSBmcm9tIFwiLi9pY29ucy9zZXR0aW5ncy5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFZpZGVvIH0gZnJvbSBcIi4vaWNvbnMvdmlkZW8uanNcIiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=BarChart3,CreditCard,Crown,HelpCircle,Home,LogOut,MessageCircle,Settings,Video!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Ftweet-center&preferredRegion=&absolutePagePath=.%2Fpages%2Ftweet-center.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Ftweet-center&preferredRegion=&absolutePagePath=.%2Fpages%2Ftweet-center.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./pages/_app.tsx\");\n/* harmony import */ var _pages_tweet_center_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages/tweet-center.tsx */ \"./pages/tweet-center.tsx\");\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_tweet_center_tsx__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_tweet_center_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_tweet_center_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_tweet_center_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_tweet_center_tsx__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_tweet_center_tsx__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_tweet_center_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_tweet_center_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_tweet_center_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_tweet_center_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_tweet_center_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/tweet-center\",\n        pathname: \"/tweet-center\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _pages_tweet_center_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Ftweet-center&preferredRegion=&absolutePagePath=.%2Fpages%2Ftweet-center.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./components/SidebarLayout.tsx":
/*!**************************************!*\
  !*** ./components/SidebarLayout.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_BarChart3_CreditCard_Crown_HelpCircle_Home_LogOut_MessageCircle_Settings_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,CreditCard,Crown,HelpCircle,Home,LogOut,MessageCircle,Settings,Video!=!lucide-react */ \"__barrel_optimize__?names=BarChart3,CreditCard,Crown,HelpCircle,Home,LogOut,MessageCircle,Settings,Video!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n\n\n\n\n\nconst SidebarLayout = ({ children })=>{\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [hoveredItem, setHoveredItem] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [showAccountMenu, setShowAccountMenu] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const accountMenuRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    // Close menu when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const handleClickOutside = (event)=>{\n            if (accountMenuRef.current && !accountMenuRef.current.contains(event.target)) {\n                setShowAccountMenu(false);\n            }\n        };\n        document.addEventListener(\"mousedown\", handleClickOutside);\n        return ()=>{\n            document.removeEventListener(\"mousedown\", handleClickOutside);\n        };\n    }, []);\n    // Warm Intelligence Theme - Focused, Intelligent, Warm\n    const colors = {\n        primary: \"#FF6B35\",\n        primaryLight: \"#FF8A65\",\n        primaryDark: \"#E65100\",\n        accent: \"#FFF3E0\",\n        surface: \"#FEFEFE\",\n        surfaceElevated: \"#FFFFFF\",\n        background: \"#FFF8F3\",\n        warmGlow: \"#FFE0B2\",\n        text: {\n            primary: \"#2D1B14\",\n            secondary: \"#5D4037\",\n            tertiary: \"#8D6E63\",\n            muted: \"#BCAAA4\",\n            inverse: \"#FFFFFF\" // White text\n        },\n        border: {\n            light: \"#F5E6D3\",\n            medium: \"#E1C4A0\",\n            primary: \"#FF6B35\" // Primary border\n        },\n        sidebar: {\n            background: \"linear-gradient(135deg, #FF6B35 0%, #FF8A65 50%, #FFB74D 100%)\",\n            backgroundSolid: \"#FF6B35\",\n            text: \"#FFFFFF\",\n            textSecondary: \"rgba(255, 255, 255, 0.9)\",\n            textTertiary: \"rgba(255, 255, 255, 0.7)\",\n            hover: \"rgba(255, 255, 255, 0.15)\",\n            active: \"rgba(255, 255, 255, 0.25)\",\n            border: \"rgba(255, 255, 255, 0.2)\",\n            glow: \"rgba(255, 107, 53, 0.3)\"\n        }\n    };\n    const menuItems = [\n        {\n            href: \"/\",\n            label: \"Briefing Room\",\n            icon: _barrel_optimize_names_BarChart3_CreditCard_Crown_HelpCircle_Home_LogOut_MessageCircle_Settings_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Home,\n            description: \"Mission Control\"\n        },\n        {\n            href: \"/tweet-center\",\n            label: \"Drafting Desk\",\n            icon: _barrel_optimize_names_BarChart3_CreditCard_Crown_HelpCircle_Home_LogOut_MessageCircle_Settings_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.MessageCircle,\n            description: \"AI Writing\"\n        },\n        {\n            href: \"/dashboard\",\n            label: \"Growth Lab\",\n            icon: _barrel_optimize_names_BarChart3_CreditCard_Crown_HelpCircle_Home_LogOut_MessageCircle_Settings_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.BarChart3,\n            description: \"Analytics\"\n        },\n        {\n            href: \"/meeting\",\n            label: \"AI Meetings\",\n            icon: _barrel_optimize_names_BarChart3_CreditCard_Crown_HelpCircle_Home_LogOut_MessageCircle_Settings_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Video,\n            description: \"Video Calls\"\n        }\n    ];\n    const isActive = (href)=>{\n        if (href === \"/\") {\n            return router.pathname === \"/\";\n        }\n        return router.pathname.startsWith(href);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            display: \"flex\",\n            minHeight: \"100vh\",\n            background: `\n        radial-gradient(circle at 20% 20%, ${colors.primary}15 0%, transparent 50%),\n        radial-gradient(circle at 80% 80%, ${colors.primaryLight}10 0%, transparent 50%),\n        linear-gradient(135deg, ${colors.primary} 0%, ${colors.primaryLight} 100%)\n      `,\n            padding: \"20px\",\n            gap: \"20px\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                style: {\n                    width: \"200px\",\n                    background: colors.sidebar.background,\n                    minHeight: \"calc(100vh - 40px)\",\n                    position: \"relative\",\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    borderRadius: \"16px\",\n                    boxShadow: `\n          0 20px 60px ${colors.sidebar.glow},\n          0 8px 32px rgba(0, 0, 0, 0.15),\n          inset 0 1px 0 rgba(255, 255, 255, 0.2)\n        `,\n                    overflow: \"hidden\",\n                    backdropFilter: \"blur(10px)\",\n                    border: `1px solid rgba(255, 255, 255, 0.1)`\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: \"24px\",\n                            borderBottom: `1px solid ${colors.sidebar.border}`,\n                            display: \"flex\",\n                            justifyContent: \"center\",\n                            alignItems: \"center\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            style: {\n                                fontSize: \"32px\",\n                                color: colors.sidebar.text,\n                                fontWeight: \"400\",\n                                fontFamily: \"Georgia, serif\",\n                                fontStyle: \"italic\",\n                                textShadow: \"0 2px 8px rgba(0, 0, 0, 0.3)\",\n                                letterSpacing: \"-1px\"\n                            },\n                            children: \"ℰ\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        style: {\n                            flex: 1,\n                            padding: \"20px 0\",\n                            overflow: \"auto\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                padding: \"0 16px\"\n                            },\n                            children: menuItems.map((item, index)=>{\n                                const active = isActive(item.href);\n                                const hovered = hoveredItem === item.href;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        marginBottom: \"6px\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: item.href,\n                                        style: {\n                                            textDecoration: \"none\"\n                                        },\n                                        onMouseEnter: ()=>setHoveredItem(item.href),\n                                        onMouseLeave: ()=>setHoveredItem(null),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                padding: \"12px 16px\",\n                                                borderRadius: \"12px\",\n                                                transition: \"all 0.2s ease\",\n                                                cursor: \"pointer\",\n                                                position: \"relative\",\n                                                background: active ? `linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.1) 100%)` : hovered ? `linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 100%)` : \"transparent\",\n                                                backdropFilter: active || hovered ? \"blur(10px)\" : \"none\",\n                                                border: active ? \"1px solid rgba(255, 255, 255, 0.3)\" : \"1px solid transparent\",\n                                                boxShadow: active ? `0 4px 16px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.4)` : \"none\"\n                                            },\n                                            children: [\n                                                active && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        position: \"absolute\",\n                                                        left: \"-1px\",\n                                                        top: \"50%\",\n                                                        transform: \"translateY(-50%)\",\n                                                        width: \"3px\",\n                                                        height: \"20px\",\n                                                        background: `linear-gradient(180deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.6) 100%)`,\n                                                        borderRadius: \"0 6px 6px 0\",\n                                                        boxShadow: \"0 0 8px rgba(255, 255, 255, 0.5)\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 189,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        width: \"24px\",\n                                                        height: \"24px\",\n                                                        display: \"flex\",\n                                                        alignItems: \"center\",\n                                                        justifyContent: \"center\",\n                                                        marginRight: \"12px\",\n                                                        borderRadius: \"6px\",\n                                                        background: active ? `linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.1) 100%)` : \"transparent\"\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                        size: 16,\n                                                        color: colors.sidebar.text,\n                                                        style: {\n                                                            filter: active ? \"drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2))\" : \"none\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                        lineNumber: 215,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"15px\",\n                                                        fontWeight: active ? \"600\" : \"500\",\n                                                        color: colors.sidebar.text,\n                                                        letterSpacing: \"-0.3px\",\n                                                        textShadow: active ? \"0 1px 2px rgba(0, 0, 0, 0.1)\" : \"none\"\n                                                    },\n                                                    children: item.label\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 225,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, item.href, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 17\n                                }, undefined);\n                            })\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: accountMenuRef,\n                        style: {\n                            padding: \"16px 12px\",\n                            borderTop: `1px solid ${colors.sidebar.border}`,\n                            marginTop: \"auto\",\n                            background: `radial-gradient(circle at center bottom, rgba(255, 255, 255, 0.1) 0%, transparent 70%)`,\n                            position: \"relative\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                onClick: ()=>setShowAccountMenu(!showAccountMenu),\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"8px\",\n                                    padding: \"8px 12px\",\n                                    background: `linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.05) 100%)`,\n                                    borderRadius: \"8px\",\n                                    cursor: \"pointer\",\n                                    transition: \"all 0.2s ease\",\n                                    backdropFilter: \"blur(10px)\",\n                                    border: \"1px solid rgba(255, 255, 255, 0.2)\",\n                                    boxShadow: `0 2px 8px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.3)`\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            width: \"24px\",\n                                            height: \"24px\",\n                                            background: `linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.1) 100%)`,\n                                            borderRadius: \"6px\",\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            justifyContent: \"center\",\n                                            position: \"relative\",\n                                            backdropFilter: \"blur(10px)\",\n                                            border: \"1px solid rgba(255, 255, 255, 0.2)\",\n                                            flexShrink: 0\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    color: colors.sidebar.text,\n                                                    fontSize: \"11px\",\n                                                    fontWeight: \"600\",\n                                                    textShadow: \"0 1px 2px rgba(0, 0, 0, 0.1)\"\n                                                },\n                                                children: \"A\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    position: \"absolute\",\n                                                    bottom: \"-1px\",\n                                                    right: \"-1px\",\n                                                    width: \"6px\",\n                                                    height: \"6px\",\n                                                    borderRadius: \"50%\",\n                                                    background: `radial-gradient(circle, #00E676 0%, #00C853 100%)`,\n                                                    border: \"1px solid rgba(255, 255, 255, 0.9)\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                lineNumber: 293,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            flex: 1,\n                                            minWidth: 0\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: \"12px\",\n                                                    fontWeight: \"600\",\n                                                    color: colors.sidebar.text,\n                                                    lineHeight: \"1.2\",\n                                                    marginBottom: \"1px\",\n                                                    textShadow: \"0 1px 2px rgba(0, 0, 0, 0.1)\",\n                                                    overflow: \"hidden\",\n                                                    textOverflow: \"ellipsis\",\n                                                    whiteSpace: \"nowrap\"\n                                                },\n                                                children: \"Alex Chen\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: \"10px\",\n                                                    color: colors.sidebar.textTertiary,\n                                                    lineHeight: \"1.2\",\n                                                    fontWeight: \"500\"\n                                                },\n                                                children: \"AI Manager\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                lineNumber: 320,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            width: \"12px\",\n                                            height: \"12px\",\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            justifyContent: \"center\",\n                                            borderRadius: \"3px\",\n                                            background: `linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.05) 100%)`,\n                                            flexShrink: 0\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                fontSize: \"8px\",\n                                                color: colors.sidebar.textSecondary,\n                                                transform: showAccountMenu ? \"rotate(180deg)\" : \"rotate(0deg)\",\n                                                transition: \"transform 0.2s ease\"\n                                            },\n                                            children: \"⌄\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 341,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                        lineNumber: 331,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 11\n                            }, undefined),\n                            showAccountMenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    position: \"absolute\",\n                                    bottom: \"100%\",\n                                    left: \"6px\",\n                                    right: \"6px\",\n                                    marginBottom: \"12px\",\n                                    background: `\n                linear-gradient(135deg,\n                  rgba(255, 255, 255, 0.95) 0%,\n                  rgba(255, 255, 255, 0.9) 50%,\n                  rgba(255, 255, 255, 0.95) 100%\n                )\n              `,\n                                    borderRadius: \"16px\",\n                                    boxShadow: `\n                0 20px 60px rgba(0, 0, 0, 0.25),\n                0 8px 32px rgba(0, 0, 0, 0.15),\n                0 4px 16px rgba(255, 107, 53, 0.2),\n                inset 0 1px 0 rgba(255, 255, 255, 0.8),\n                inset 0 -1px 0 rgba(255, 107, 53, 0.1)\n              `,\n                                    border: `1px solid rgba(255, 107, 53, 0.2)`,\n                                    overflow: \"hidden\",\n                                    zIndex: 1000,\n                                    backdropFilter: \"blur(20px)\",\n                                    animation: \"slideUp 0.2s ease-out\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            padding: \"16px 16px 12px 16px\",\n                                            borderBottom: `1px solid rgba(255, 107, 53, 0.15)`,\n                                            background: `linear-gradient(135deg, rgba(255, 107, 53, 0.1) 0%, rgba(255, 138, 101, 0.05) 100%)`\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                gap: \"10px\"\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        width: \"32px\",\n                                                        height: \"32px\",\n                                                        background: `linear-gradient(135deg, ${colors.primary} 0%, ${colors.primaryLight} 100%)`,\n                                                        borderRadius: \"8px\",\n                                                        display: \"flex\",\n                                                        alignItems: \"center\",\n                                                        justifyContent: \"center\",\n                                                        boxShadow: \"0 4px 12px rgba(255, 107, 53, 0.3)\",\n                                                        position: \"relative\"\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            style: {\n                                                                color: \"white\",\n                                                                fontSize: \"14px\",\n                                                                fontWeight: \"700\",\n                                                                textShadow: \"0 1px 2px rgba(0, 0, 0, 0.2)\"\n                                                            },\n                                                            children: \"A\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                            lineNumber: 403,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_CreditCard_Crown_HelpCircle_Home_LogOut_MessageCircle_Settings_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Crown, {\n                                                            size: 10,\n                                                            color: \"gold\",\n                                                            style: {\n                                                                position: \"absolute\",\n                                                                top: \"-2px\",\n                                                                right: \"-2px\",\n                                                                filter: \"drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3))\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                            lineNumber: 411,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 392,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                fontSize: \"15px\",\n                                                                fontWeight: \"700\",\n                                                                color: colors.text.primary,\n                                                                lineHeight: \"1.2\",\n                                                                marginBottom: \"2px\"\n                                                            },\n                                                            children: \"Alex Chen\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                            lineNumber: 423,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                fontSize: \"12px\",\n                                                                color: colors.text.secondary,\n                                                                fontWeight: \"500\",\n                                                                display: \"flex\",\n                                                                alignItems: \"center\",\n                                                                gap: \"4px\"\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        width: \"6px\",\n                                                                        height: \"6px\",\n                                                                        borderRadius: \"50%\",\n                                                                        background: `linear-gradient(135deg, #00E676 0%, #00C853 100%)`,\n                                                                        boxShadow: \"0 0 4px rgba(0, 230, 118, 0.5)\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                                    lineNumber: 440,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                \"Pro Member\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                            lineNumber: 432,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 422,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 387,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                        lineNumber: 382,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    [\n                                        {\n                                            label: \"Account Settings\",\n                                            icon: _barrel_optimize_names_BarChart3_CreditCard_Crown_HelpCircle_Home_LogOut_MessageCircle_Settings_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Settings,\n                                            description: \"Manage your profile\"\n                                        },\n                                        {\n                                            label: \"Subscription\",\n                                            icon: _barrel_optimize_names_BarChart3_CreditCard_Crown_HelpCircle_Home_LogOut_MessageCircle_Settings_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Crown,\n                                            description: \"Pro plan details\"\n                                        },\n                                        {\n                                            label: \"Billing\",\n                                            icon: _barrel_optimize_names_BarChart3_CreditCard_Crown_HelpCircle_Home_LogOut_MessageCircle_Settings_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.CreditCard,\n                                            description: \"Payment & invoices\"\n                                        },\n                                        {\n                                            label: \"Help & Support\",\n                                            icon: _barrel_optimize_names_BarChart3_CreditCard_Crown_HelpCircle_Home_LogOut_MessageCircle_Settings_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.HelpCircle,\n                                            description: \"24/7 assistance\"\n                                        },\n                                        {\n                                            label: \"Sign Out\",\n                                            icon: _barrel_optimize_names_BarChart3_CreditCard_Crown_HelpCircle_Home_LogOut_MessageCircle_Settings_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.LogOut,\n                                            description: \"Secure logout\",\n                                            isDestructive: true\n                                        }\n                                    ].map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                padding: \"14px 16px\",\n                                                cursor: \"pointer\",\n                                                transition: \"all 0.2s ease\",\n                                                borderBottom: index < 4 ? `1px solid rgba(255, 107, 53, 0.08)` : \"none\",\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                gap: \"12px\",\n                                                background: \"transparent\",\n                                                position: \"relative\"\n                                            },\n                                            onMouseEnter: (e)=>{\n                                                e.currentTarget.style.background = item.isDestructive ? `linear-gradient(135deg, rgba(231, 76, 60, 0.1) 0%, rgba(231, 76, 60, 0.05) 100%)` : `linear-gradient(135deg, rgba(255, 107, 53, 0.15) 0%, rgba(255, 138, 101, 0.08) 100%)`;\n                                                e.currentTarget.style.transform = \"translateX(2px)\";\n                                            },\n                                            onMouseLeave: (e)=>{\n                                                e.currentTarget.style.background = \"transparent\";\n                                                e.currentTarget.style.transform = \"translateX(0px)\";\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        width: \"36px\",\n                                                        height: \"36px\",\n                                                        borderRadius: \"10px\",\n                                                        background: item.isDestructive ? `linear-gradient(135deg, rgba(231, 76, 60, 0.15) 0%, rgba(231, 76, 60, 0.08) 100%)` : `linear-gradient(135deg, rgba(255, 107, 53, 0.15) 0%, rgba(255, 138, 101, 0.08) 100%)`,\n                                                        display: \"flex\",\n                                                        alignItems: \"center\",\n                                                        justifyContent: \"center\",\n                                                        border: `1px solid ${item.isDestructive ? \"rgba(231, 76, 60, 0.2)\" : \"rgba(255, 107, 53, 0.2)\"}`,\n                                                        boxShadow: `0 2px 8px ${item.isDestructive ? \"rgba(231, 76, 60, 0.1)\" : \"rgba(255, 107, 53, 0.1)\"}`,\n                                                        flexShrink: 0\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                        size: 16,\n                                                        color: item.isDestructive ? \"#e74c3c\" : colors.primary,\n                                                        style: {\n                                                            filter: `drop-shadow(0 1px 2px ${item.isDestructive ? \"rgba(231, 76, 60, 0.2)\" : \"rgba(255, 107, 53, 0.2)\"})`\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                        lineNumber: 500,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 486,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        flex: 1\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                fontSize: \"14px\",\n                                                                fontWeight: \"600\",\n                                                                color: item.isDestructive ? \"#e74c3c\" : colors.text.primary,\n                                                                lineHeight: \"1.3\",\n                                                                marginBottom: \"2px\",\n                                                                textShadow: \"0 1px 2px rgba(0, 0, 0, 0.05)\"\n                                                            },\n                                                            children: item.label\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                            lineNumber: 511,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                fontSize: \"11px\",\n                                                                color: colors.text.tertiary,\n                                                                fontWeight: \"500\",\n                                                                lineHeight: \"1.2\"\n                                                            },\n                                                            children: item.description\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                            lineNumber: 521,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 510,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        width: \"20px\",\n                                                        height: \"20px\",\n                                                        borderRadius: \"6px\",\n                                                        background: `linear-gradient(135deg, rgba(255, 107, 53, 0.1) 0%, rgba(255, 138, 101, 0.05) 100%)`,\n                                                        display: \"flex\",\n                                                        alignItems: \"center\",\n                                                        justifyContent: \"center\",\n                                                        opacity: 0.6,\n                                                        transition: \"all 0.2s ease\"\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            fontSize: \"10px\",\n                                                            color: colors.text.secondary,\n                                                            transform: \"rotate(-90deg)\"\n                                                        },\n                                                        children: \"⌄\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                        lineNumber: 543,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 532,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 461,\n                                            columnNumber: 17\n                                        }, undefined))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                        lineNumber: 243,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                lineNumber: 112,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                style: {\n                    flexGrow: 1,\n                    position: \"relative\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        backgroundColor: colors.surfaceElevated,\n                        borderRadius: \"20px\",\n                        minHeight: \"calc(100vh - 40px)\",\n                        boxShadow: `\n            0 32px 80px rgba(0, 0, 0, 0.12),\n            0 8px 32px rgba(0, 0, 0, 0.08),\n            inset 0 1px 0 rgba(255, 255, 255, 0.9),\n            0 0 0 1px rgba(255, 107, 53, 0.1)\n          `,\n                        overflow: \"hidden\",\n                        position: \"relative\",\n                        backdropFilter: \"blur(20px)\",\n                        border: \"1px solid rgba(255, 255, 255, 0.2)\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                position: \"absolute\",\n                                top: 0,\n                                left: 0,\n                                right: 0,\n                                height: \"200px\",\n                                background: `\n              radial-gradient(ellipse at top, ${colors.warmGlow}20 0%, transparent 70%)\n            `,\n                                pointerEvents: \"none\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                            lineNumber: 579,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                position: \"relative\",\n                                zIndex: 1,\n                                height: \"100%\"\n                            },\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                            lineNumber: 592,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                    lineNumber: 563,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                lineNumber: 559,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n        lineNumber: 100,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SidebarLayout);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL1NpZGViYXJMYXlvdXQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBQTZCO0FBQzhCO0FBQ25CO0FBQ2dHO0FBTXhJLE1BQU1lLGdCQUE4QyxDQUFDLEVBQUVDLFFBQVEsRUFBRTtJQUMvRCxNQUFNQyxTQUFTWixzREFBU0E7SUFDeEIsTUFBTSxDQUFDYSxhQUFhQyxlQUFlLEdBQUdqQiwrQ0FBUUEsQ0FBZ0I7SUFDOUQsTUFBTSxDQUFDa0IsaUJBQWlCQyxtQkFBbUIsR0FBR25CLCtDQUFRQSxDQUFDO0lBQ3ZELE1BQU1vQixpQkFBaUJsQiw2Q0FBTUEsQ0FBaUI7SUFFOUMsbUNBQW1DO0lBQ25DRCxnREFBU0EsQ0FBQztRQUNSLE1BQU1vQixxQkFBcUIsQ0FBQ0M7WUFDMUIsSUFBSUYsZUFBZUcsT0FBTyxJQUFJLENBQUNILGVBQWVHLE9BQU8sQ0FBQ0MsUUFBUSxDQUFDRixNQUFNRyxNQUFNLEdBQVc7Z0JBQ3BGTixtQkFBbUI7WUFDckI7UUFDRjtRQUVBTyxTQUFTQyxnQkFBZ0IsQ0FBQyxhQUFhTjtRQUN2QyxPQUFPO1lBQ0xLLFNBQVNFLG1CQUFtQixDQUFDLGFBQWFQO1FBQzVDO0lBQ0YsR0FBRyxFQUFFO0lBRUwsdURBQXVEO0lBQ3ZELE1BQU1RLFNBQVM7UUFDYkMsU0FBUztRQUNUQyxjQUFjO1FBQ2RDLGFBQWE7UUFDYkMsUUFBUTtRQUNSQyxTQUFTO1FBQ1RDLGlCQUFpQjtRQUNqQkMsWUFBWTtRQUNaQyxVQUFVO1FBQ1ZDLE1BQU07WUFDSlIsU0FBUztZQUNUUyxXQUFXO1lBQ1hDLFVBQVU7WUFDVkMsT0FBTztZQUNQQyxTQUFTLFVBQWdCLGFBQWE7UUFDeEM7UUFDQUMsUUFBUTtZQUNOQyxPQUFPO1lBQ1BDLFFBQVE7WUFDUmYsU0FBUyxVQUFnQixpQkFBaUI7UUFDNUM7UUFDQWdCLFNBQVM7WUFDUFYsWUFBWTtZQUNaVyxpQkFBaUI7WUFDakJULE1BQU07WUFDTlUsZUFBZTtZQUNmQyxjQUFjO1lBQ2RDLE9BQU87WUFDUEMsUUFBUTtZQUNSUixRQUFRO1lBQ1JTLE1BQU07UUFDUjtJQUNGO0lBRUEsTUFBTUMsWUFBWTtRQUNoQjtZQUNFQyxNQUFNO1lBQ05DLE9BQU87WUFDUEMsTUFBTXBELG9KQUFJQTtZQUNWcUQsYUFBYTtRQUNmO1FBQ0E7WUFDRUgsTUFBTTtZQUNOQyxPQUFPO1lBQ1BDLE1BQU1sRCw2SkFBYUE7WUFDbkJtRCxhQUFhO1FBQ2Y7UUFDQTtZQUNFSCxNQUFNO1lBQ05DLE9BQU87WUFDUEMsTUFBTW5ELHlKQUFTQTtZQUNmb0QsYUFBYTtRQUNmO1FBQ0E7WUFDRUgsTUFBTTtZQUNOQyxPQUFPO1lBQ1BDLE1BQU1qRCxxSkFBS0E7WUFDWGtELGFBQWE7UUFDZjtLQUNEO0lBRUQsTUFBTUMsV0FBVyxDQUFDSjtRQUNoQixJQUFJQSxTQUFTLEtBQUs7WUFDaEIsT0FBT3ZDLE9BQU80QyxRQUFRLEtBQUs7UUFDN0I7UUFDQSxPQUFPNUMsT0FBTzRDLFFBQVEsQ0FBQ0MsVUFBVSxDQUFDTjtJQUNwQztJQUVBLHFCQUNFLDhEQUFDTztRQUFJQyxPQUFPO1lBQ1ZDLFNBQVM7WUFDVEMsV0FBVztZQUNYNUIsWUFBWSxDQUFDOzJDQUN3QixFQUFFUCxPQUFPQyxPQUFPLENBQUM7MkNBQ2pCLEVBQUVELE9BQU9FLFlBQVksQ0FBQztnQ0FDakMsRUFBRUYsT0FBT0MsT0FBTyxDQUFDLEtBQUssRUFBRUQsT0FBT0UsWUFBWSxDQUFDO01BQ3RFLENBQUM7WUFDRGtDLFNBQVM7WUFDVEMsS0FBSztRQUNQOzswQkFFRSw4REFBQ0M7Z0JBQU1MLE9BQU87b0JBQ1pNLE9BQU87b0JBQ1BoQyxZQUFZUCxPQUFPaUIsT0FBTyxDQUFDVixVQUFVO29CQUNyQzRCLFdBQVc7b0JBQ1hLLFVBQVU7b0JBQ1ZOLFNBQVM7b0JBQ1RPLGVBQWU7b0JBQ2ZDLGNBQWM7b0JBQ2RDLFdBQVcsQ0FBQztzQkFDRSxFQUFFM0MsT0FBT2lCLE9BQU8sQ0FBQ00sSUFBSSxDQUFDOzs7UUFHcEMsQ0FBQztvQkFDRHFCLFVBQVU7b0JBQ1ZDLGdCQUFnQjtvQkFDaEIvQixRQUFRLENBQUMsa0NBQWtDLENBQUM7Z0JBQzlDOztrQ0FFRSw4REFBQ2tCO3dCQUFJQyxPQUFPOzRCQUNWRyxTQUFTOzRCQUNUVSxjQUFjLENBQUMsVUFBVSxFQUFFOUMsT0FBT2lCLE9BQU8sQ0FBQ0gsTUFBTSxDQUFDLENBQUM7NEJBQ2xEb0IsU0FBUzs0QkFDVGEsZ0JBQWdCOzRCQUNoQkMsWUFBWTt3QkFDZDtrQ0FFRSw0RUFBQ0M7NEJBQUtoQixPQUFPO2dDQUNYaUIsVUFBVTtnQ0FDVkMsT0FBT25ELE9BQU9pQixPQUFPLENBQUNSLElBQUk7Z0NBQzFCMkMsWUFBWTtnQ0FDWkMsWUFBWTtnQ0FDWkMsV0FBVztnQ0FDWEMsWUFBWTtnQ0FDWkMsZUFBZTs0QkFDakI7c0NBQUc7Ozs7Ozs7Ozs7O2tDQU1MLDhEQUFDQzt3QkFBSXhCLE9BQU87NEJBQUV5QixNQUFNOzRCQUFHdEIsU0FBUzs0QkFBVVEsVUFBVTt3QkFBTztrQ0FDekQsNEVBQUNaOzRCQUFJQyxPQUFPO2dDQUFFRyxTQUFTOzRCQUFTO3NDQUM3QlosVUFBVW1DLEdBQUcsQ0FBQyxDQUFDQyxNQUFNQztnQ0FDcEIsTUFBTXZDLFNBQVNPLFNBQVMrQixLQUFLbkMsSUFBSTtnQ0FDakMsTUFBTXFDLFVBQVUzRSxnQkFBZ0J5RSxLQUFLbkMsSUFBSTtnQ0FFekMscUJBQ0UsOERBQUNPO29DQUFvQkMsT0FBTzt3Q0FBRThCLGNBQWM7b0NBQU07OENBQ2hELDRFQUFDOUYsa0RBQUlBO3dDQUNId0QsTUFBTW1DLEtBQUtuQyxJQUFJO3dDQUNmUSxPQUFPOzRDQUFFK0IsZ0JBQWdCO3dDQUFPO3dDQUNoQ0MsY0FBYyxJQUFNN0UsZUFBZXdFLEtBQUtuQyxJQUFJO3dDQUM1Q3lDLGNBQWMsSUFBTTlFLGVBQWU7a0RBRW5DLDRFQUFDNEM7NENBQUlDLE9BQU87Z0RBQ1ZDLFNBQVM7Z0RBQ1RjLFlBQVk7Z0RBQ1paLFNBQVM7Z0RBQ1RNLGNBQWM7Z0RBQ2R5QixZQUFZO2dEQUNaQyxRQUFRO2dEQUNSNUIsVUFBVTtnREFDVmpDLFlBQVllLFNBQ1IsQ0FBQyxvRkFBb0YsQ0FBQyxHQUN0RndDLFVBQ0UsQ0FBQyxxRkFBcUYsQ0FBQyxHQUN2RjtnREFDTmpCLGdCQUFnQixVQUFXaUIsVUFBVyxlQUFlO2dEQUNyRGhELFFBQVFRLFNBQ0osdUNBQ0E7Z0RBQ0pxQixXQUFXckIsU0FDUCxDQUFDLHFFQUFxRSxDQUFDLEdBQ3ZFOzRDQUNOOztnREFFR0Esd0JBQ0MsOERBQUNVO29EQUFJQyxPQUFPO3dEQUNWTyxVQUFVO3dEQUNWNkIsTUFBTTt3REFDTkMsS0FBSzt3REFDTEMsV0FBVzt3REFDWGhDLE9BQU87d0RBQ1BpQyxRQUFRO3dEQUNSakUsWUFBWSxDQUFDLG1GQUFtRixDQUFDO3dEQUNqR21DLGNBQWM7d0RBQ2RDLFdBQVc7b0RBQ2I7Ozs7Ozs4REFJRiw4REFBQ1g7b0RBQUlDLE9BQU87d0RBQ1ZNLE9BQU87d0RBQ1BpQyxRQUFRO3dEQUNSdEMsU0FBUzt3REFDVGMsWUFBWTt3REFDWkQsZ0JBQWdCO3dEQUNoQjBCLGFBQWE7d0RBQ2IvQixjQUFjO3dEQUNkbkMsWUFBWWUsU0FDUixDQUFDLG1GQUFtRixDQUFDLEdBQ3JGO29EQUNOOzhEQUNFLDRFQUFDc0MsS0FBS2pDLElBQUk7d0RBQ1IrQyxNQUFNO3dEQUNOdkIsT0FBT25ELE9BQU9pQixPQUFPLENBQUNSLElBQUk7d0RBQzFCd0IsT0FBTzs0REFDTDBDLFFBQVFyRCxTQUFTLDhDQUE4Qzt3REFDakU7Ozs7Ozs7Ozs7OzhEQUtKLDhEQUFDVTtvREFBSUMsT0FBTzt3REFDVmlCLFVBQVU7d0RBQ1ZFLFlBQVk5QixTQUFTLFFBQVE7d0RBQzdCNkIsT0FBT25ELE9BQU9pQixPQUFPLENBQUNSLElBQUk7d0RBQzFCK0MsZUFBZTt3REFDZkQsWUFBWWpDLFNBQVMsaUNBQWlDO29EQUN4RDs4REFDR3NDLEtBQUtsQyxLQUFLOzs7Ozs7Ozs7Ozs7Ozs7OzttQ0F6RVRrQyxLQUFLbkMsSUFBSTs7Ozs7NEJBK0V2Qjs7Ozs7Ozs7Ozs7a0NBS0osOERBQUNPO3dCQUNDNEMsS0FBS3JGO3dCQUNMMEMsT0FBTzs0QkFDTEcsU0FBUzs0QkFDVHlDLFdBQVcsQ0FBQyxVQUFVLEVBQUU3RSxPQUFPaUIsT0FBTyxDQUFDSCxNQUFNLENBQUMsQ0FBQzs0QkFDL0NnRSxXQUFXOzRCQUNYdkUsWUFBWSxDQUFDLHNGQUFzRixDQUFDOzRCQUNwR2lDLFVBQVU7d0JBQ1o7OzBDQUdBLDhEQUFDUjtnQ0FDQytDLFNBQVMsSUFBTXpGLG1CQUFtQixDQUFDRDtnQ0FDbkM0QyxPQUFPO29DQUNMQyxTQUFTO29DQUNUYyxZQUFZO29DQUNaWCxLQUFLO29DQUNMRCxTQUFTO29DQUNUN0IsWUFBWSxDQUFDLG9GQUFvRixDQUFDO29DQUNsR21DLGNBQWM7b0NBQ2QwQixRQUFRO29DQUNSRCxZQUFZO29DQUNadEIsZ0JBQWdCO29DQUNoQi9CLFFBQVE7b0NBQ1I2QixXQUFXLENBQUMsb0VBQW9FLENBQUM7Z0NBQ25GOztrREFHQSw4REFBQ1g7d0NBQUlDLE9BQU87NENBQ1ZNLE9BQU87NENBQ1BpQyxRQUFROzRDQUNSakUsWUFBWSxDQUFDLG1GQUFtRixDQUFDOzRDQUNqR21DLGNBQWM7NENBQ2RSLFNBQVM7NENBQ1RjLFlBQVk7NENBQ1pELGdCQUFnQjs0Q0FDaEJQLFVBQVU7NENBQ1ZLLGdCQUFnQjs0Q0FDaEIvQixRQUFROzRDQUNSa0UsWUFBWTt3Q0FDZDs7MERBQ0UsOERBQUMvQjtnREFBS2hCLE9BQU87b0RBQ1hrQixPQUFPbkQsT0FBT2lCLE9BQU8sQ0FBQ1IsSUFBSTtvREFDMUJ5QyxVQUFVO29EQUNWRSxZQUFZO29EQUNaRyxZQUFZO2dEQUNkOzBEQUFHOzs7Ozs7MERBSUgsOERBQUN2QjtnREFBSUMsT0FBTztvREFDVk8sVUFBVTtvREFDVnlDLFFBQVE7b0RBQ1JDLE9BQU87b0RBQ1AzQyxPQUFPO29EQUNQaUMsUUFBUTtvREFDUjlCLGNBQWM7b0RBQ2RuQyxZQUFZLENBQUMsaURBQWlELENBQUM7b0RBQy9ETyxRQUFRO2dEQUNWOzs7Ozs7Ozs7Ozs7a0RBSUYsOERBQUNrQjt3Q0FBSUMsT0FBTzs0Q0FBRXlCLE1BQU07NENBQUd5QixVQUFVO3dDQUFFOzswREFDakMsOERBQUNuRDtnREFBSUMsT0FBTztvREFDVmlCLFVBQVU7b0RBQ1ZFLFlBQVk7b0RBQ1pELE9BQU9uRCxPQUFPaUIsT0FBTyxDQUFDUixJQUFJO29EQUMxQjJFLFlBQVk7b0RBQ1pyQixjQUFjO29EQUNkUixZQUFZO29EQUNaWCxVQUFVO29EQUNWeUMsY0FBYztvREFDZEMsWUFBWTtnREFDZDswREFBRzs7Ozs7OzBEQUdILDhEQUFDdEQ7Z0RBQUlDLE9BQU87b0RBQ1ZpQixVQUFVO29EQUNWQyxPQUFPbkQsT0FBT2lCLE9BQU8sQ0FBQ0csWUFBWTtvREFDbENnRSxZQUFZO29EQUNaaEMsWUFBWTtnREFDZDswREFBRzs7Ozs7Ozs7Ozs7O2tEQU1MLDhEQUFDcEI7d0NBQUlDLE9BQU87NENBQ1ZNLE9BQU87NENBQ1BpQyxRQUFROzRDQUNSdEMsU0FBUzs0Q0FDVGMsWUFBWTs0Q0FDWkQsZ0JBQWdCOzRDQUNoQkwsY0FBYzs0Q0FDZG5DLFlBQVksQ0FBQyxvRkFBb0YsQ0FBQzs0Q0FDbEd5RSxZQUFZO3dDQUNkO2tEQUNFLDRFQUFDL0I7NENBQUtoQixPQUFPO2dEQUNYaUIsVUFBVTtnREFDVkMsT0FBT25ELE9BQU9pQixPQUFPLENBQUNFLGFBQWE7Z0RBQ25Db0QsV0FBV2xGLGtCQUFrQixtQkFBbUI7Z0RBQ2hEOEUsWUFBWTs0Q0FDZDtzREFBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7NEJBT045RSxpQ0FDQyw4REFBQzJDO2dDQUFJQyxPQUFPO29DQUNWTyxVQUFVO29DQUNWeUMsUUFBUTtvQ0FDUlosTUFBTTtvQ0FDTmEsT0FBTztvQ0FDUG5CLGNBQWM7b0NBQ2R4RCxZQUFZLENBQUM7Ozs7OztjQU1iLENBQUM7b0NBQ0RtQyxjQUFjO29DQUNkQyxXQUFXLENBQUM7Ozs7OztjQU1aLENBQUM7b0NBQ0Q3QixRQUFRLENBQUMsaUNBQWlDLENBQUM7b0NBQzNDOEIsVUFBVTtvQ0FDVjJDLFFBQVE7b0NBQ1IxQyxnQkFBZ0I7b0NBQ2hCMkMsV0FBVztnQ0FDYjs7a0RBRUUsOERBQUN4RDt3Q0FBSUMsT0FBTzs0Q0FDVkcsU0FBUzs0Q0FDVFUsY0FBYyxDQUFDLGtDQUFrQyxDQUFDOzRDQUNsRHZDLFlBQVksQ0FBQyxtRkFBbUYsQ0FBQzt3Q0FDbkc7a0RBQ0UsNEVBQUN5Qjs0Q0FBSUMsT0FBTztnREFDVkMsU0FBUztnREFDVGMsWUFBWTtnREFDWlgsS0FBSzs0Q0FDUDs7OERBQ0UsOERBQUNMO29EQUFJQyxPQUFPO3dEQUNWTSxPQUFPO3dEQUNQaUMsUUFBUTt3REFDUmpFLFlBQVksQ0FBQyx3QkFBd0IsRUFBRVAsT0FBT0MsT0FBTyxDQUFDLEtBQUssRUFBRUQsT0FBT0UsWUFBWSxDQUFDLE1BQU0sQ0FBQzt3REFDeEZ3QyxjQUFjO3dEQUNkUixTQUFTO3dEQUNUYyxZQUFZO3dEQUNaRCxnQkFBZ0I7d0RBQ2hCSixXQUFXO3dEQUNYSCxVQUFVO29EQUNaOztzRUFDRSw4REFBQ1M7NERBQUtoQixPQUFPO2dFQUNYa0IsT0FBTztnRUFDUEQsVUFBVTtnRUFDVkUsWUFBWTtnRUFDWkcsWUFBWTs0REFDZDtzRUFBRzs7Ozs7O3NFQUdILDhEQUFDeEUscUpBQUtBOzREQUNKMkYsTUFBTTs0REFDTnZCLE9BQU07NERBQ05sQixPQUFPO2dFQUNMTyxVQUFVO2dFQUNWOEIsS0FBSztnRUFDTFksT0FBTztnRUFDUFAsUUFBUTs0REFDVjs7Ozs7Ozs7Ozs7OzhEQUdKLDhEQUFDM0M7O3NFQUNDLDhEQUFDQTs0REFBSUMsT0FBTztnRUFDVmlCLFVBQVU7Z0VBQ1ZFLFlBQVk7Z0VBQ1pELE9BQU9uRCxPQUFPUyxJQUFJLENBQUNSLE9BQU87Z0VBQzFCbUYsWUFBWTtnRUFDWnJCLGNBQWM7NERBQ2hCO3NFQUFHOzs7Ozs7c0VBR0gsOERBQUMvQjs0REFBSUMsT0FBTztnRUFDVmlCLFVBQVU7Z0VBQ1ZDLE9BQU9uRCxPQUFPUyxJQUFJLENBQUNDLFNBQVM7Z0VBQzVCMEMsWUFBWTtnRUFDWmxCLFNBQVM7Z0VBQ1RjLFlBQVk7Z0VBQ1pYLEtBQUs7NERBQ1A7OzhFQUNFLDhEQUFDTDtvRUFBSUMsT0FBTzt3RUFDVk0sT0FBTzt3RUFDUGlDLFFBQVE7d0VBQ1I5QixjQUFjO3dFQUNkbkMsWUFBWSxDQUFDLGlEQUFpRCxDQUFDO3dFQUMvRG9DLFdBQVc7b0VBQ2I7Ozs7OztnRUFBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O29DQVFaO3dDQUNDOzRDQUFFakIsT0FBTzs0Q0FBb0JDLE1BQU1oRCx3SkFBUUE7NENBQUVpRCxhQUFhO3dDQUFzQjt3Q0FDaEY7NENBQUVGLE9BQU87NENBQWdCQyxNQUFNNUMscUpBQUtBOzRDQUFFNkMsYUFBYTt3Q0FBbUI7d0NBQ3RFOzRDQUFFRixPQUFPOzRDQUFXQyxNQUFNL0MsMEpBQVVBOzRDQUFFZ0QsYUFBYTt3Q0FBcUI7d0NBQ3hFOzRDQUFFRixPQUFPOzRDQUFrQkMsTUFBTTlDLDBKQUFVQTs0Q0FBRStDLGFBQWE7d0NBQWtCO3dDQUM1RTs0Q0FBRUYsT0FBTzs0Q0FBWUMsTUFBTTdDLHNKQUFNQTs0Q0FBRThDLGFBQWE7NENBQWlCNkQsZUFBZTt3Q0FBSztxQ0FDdEYsQ0FBQzlCLEdBQUcsQ0FBQyxDQUFDQyxNQUFNQyxzQkFDWCw4REFBQzdCOzRDQUVDQyxPQUFPO2dEQUNMRyxTQUFTO2dEQUNUZ0MsUUFBUTtnREFDUkQsWUFBWTtnREFDWnJCLGNBQWNlLFFBQVEsSUFBSSxDQUFDLGtDQUFrQyxDQUFDLEdBQUc7Z0RBQ2pFM0IsU0FBUztnREFDVGMsWUFBWTtnREFDWlgsS0FBSztnREFDTDlCLFlBQVk7Z0RBQ1ppQyxVQUFVOzRDQUNaOzRDQUNBeUIsY0FBYyxDQUFDeUI7Z0RBQ2JBLEVBQUVDLGFBQWEsQ0FBQzFELEtBQUssQ0FBQzFCLFVBQVUsR0FBR3FELEtBQUs2QixhQUFhLEdBQ2pELENBQUMsZ0ZBQWdGLENBQUMsR0FDbEYsQ0FBQyxvRkFBb0YsQ0FBQztnREFDMUZDLEVBQUVDLGFBQWEsQ0FBQzFELEtBQUssQ0FBQ3NDLFNBQVMsR0FBRzs0Q0FDcEM7NENBQ0FMLGNBQWMsQ0FBQ3dCO2dEQUNiQSxFQUFFQyxhQUFhLENBQUMxRCxLQUFLLENBQUMxQixVQUFVLEdBQUc7Z0RBQ25DbUYsRUFBRUMsYUFBYSxDQUFDMUQsS0FBSyxDQUFDc0MsU0FBUyxHQUFHOzRDQUNwQzs7OERBR0EsOERBQUN2QztvREFBSUMsT0FBTzt3REFDVk0sT0FBTzt3REFDUGlDLFFBQVE7d0RBQ1I5QixjQUFjO3dEQUNkbkMsWUFBWXFELEtBQUs2QixhQUFhLEdBQzFCLENBQUMsaUZBQWlGLENBQUMsR0FDbkYsQ0FBQyxvRkFBb0YsQ0FBQzt3REFDMUZ2RCxTQUFTO3dEQUNUYyxZQUFZO3dEQUNaRCxnQkFBZ0I7d0RBQ2hCakMsUUFBUSxDQUFDLFVBQVUsRUFBRThDLEtBQUs2QixhQUFhLEdBQUcsMkJBQTJCLDBCQUEwQixDQUFDO3dEQUNoRzlDLFdBQVcsQ0FBQyxVQUFVLEVBQUVpQixLQUFLNkIsYUFBYSxHQUFHLDJCQUEyQiwwQkFBMEIsQ0FBQzt3REFDbkdULFlBQVk7b0RBQ2Q7OERBQ0UsNEVBQUNwQixLQUFLakMsSUFBSTt3REFDUitDLE1BQU07d0RBQ052QixPQUFPUyxLQUFLNkIsYUFBYSxHQUFHLFlBQVl6RixPQUFPQyxPQUFPO3dEQUN0RGdDLE9BQU87NERBQ0wwQyxRQUFRLENBQUMsc0JBQXNCLEVBQUVmLEtBQUs2QixhQUFhLEdBQUcsMkJBQTJCLDBCQUEwQixDQUFDLENBQUM7d0RBQy9HOzs7Ozs7Ozs7Ozs4REFLSiw4REFBQ3pEO29EQUFJQyxPQUFPO3dEQUFFeUIsTUFBTTtvREFBRTs7c0VBQ3BCLDhEQUFDMUI7NERBQUlDLE9BQU87Z0VBQ1ZpQixVQUFVO2dFQUNWRSxZQUFZO2dFQUNaRCxPQUFPUyxLQUFLNkIsYUFBYSxHQUFHLFlBQVl6RixPQUFPUyxJQUFJLENBQUNSLE9BQU87Z0VBQzNEbUYsWUFBWTtnRUFDWnJCLGNBQWM7Z0VBQ2RSLFlBQVk7NERBQ2Q7c0VBQ0dLLEtBQUtsQyxLQUFLOzs7Ozs7c0VBRWIsOERBQUNNOzREQUFJQyxPQUFPO2dFQUNWaUIsVUFBVTtnRUFDVkMsT0FBT25ELE9BQU9TLElBQUksQ0FBQ0UsUUFBUTtnRUFDM0J5QyxZQUFZO2dFQUNaZ0MsWUFBWTs0REFDZDtzRUFDR3hCLEtBQUtoQyxXQUFXOzs7Ozs7Ozs7Ozs7OERBS3JCLDhEQUFDSTtvREFBSUMsT0FBTzt3REFDVk0sT0FBTzt3REFDUGlDLFFBQVE7d0RBQ1I5QixjQUFjO3dEQUNkbkMsWUFBWSxDQUFDLG1GQUFtRixDQUFDO3dEQUNqRzJCLFNBQVM7d0RBQ1RjLFlBQVk7d0RBQ1pELGdCQUFnQjt3REFDaEI2QyxTQUFTO3dEQUNUekIsWUFBWTtvREFDZDs4REFDRSw0RUFBQ2xCO3dEQUFLaEIsT0FBTzs0REFDWGlCLFVBQVU7NERBQ1ZDLE9BQU9uRCxPQUFPUyxJQUFJLENBQUNDLFNBQVM7NERBQzVCNkQsV0FBVzt3REFDYjtrRUFBRzs7Ozs7Ozs7Ozs7OzJDQXJGQVY7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQWlHakIsOERBQUNnQztnQkFBSzVELE9BQU87b0JBQ1g2RCxVQUFVO29CQUNWdEQsVUFBVTtnQkFDWjswQkFDRSw0RUFBQ1I7b0JBQUlDLE9BQU87d0JBQ1Y4RCxpQkFBaUIvRixPQUFPTSxlQUFlO3dCQUN2Q29DLGNBQWM7d0JBQ2RQLFdBQVc7d0JBQ1hRLFdBQVcsQ0FBQzs7Ozs7VUFLWixDQUFDO3dCQUNEQyxVQUFVO3dCQUNWSixVQUFVO3dCQUNWSyxnQkFBZ0I7d0JBQ2hCL0IsUUFBUTtvQkFDVjs7c0NBRUUsOERBQUNrQjs0QkFBSUMsT0FBTztnQ0FDVk8sVUFBVTtnQ0FDVjhCLEtBQUs7Z0NBQ0xELE1BQU07Z0NBQ05hLE9BQU87Z0NBQ1BWLFFBQVE7Z0NBQ1JqRSxZQUFZLENBQUM7OENBQ3FCLEVBQUVQLE9BQU9RLFFBQVEsQ0FBQztZQUNwRCxDQUFDO2dDQUNEd0YsZUFBZTs0QkFDakI7Ozs7OztzQ0FHQSw4REFBQ2hFOzRCQUFJQyxPQUFPO2dDQUNWTyxVQUFVO2dDQUNWK0MsUUFBUTtnQ0FDUmYsUUFBUTs0QkFDVjtzQ0FDR3ZGOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU1iO0FBRUEsaUVBQWVELGFBQWFBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9leGllLWFpLy4vY29tcG9uZW50cy9TaWRlYmFyTGF5b3V0LnRzeD81MTNkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBMaW5rIGZyb20gJ25leHQvbGluayc7XG5pbXBvcnQgUmVhY3QsIHsgdXNlU3RhdGUsIHVzZUVmZmVjdCwgdXNlUmVmIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgdXNlUm91dGVyIH0gZnJvbSAnbmV4dC9yb3V0ZXInO1xuaW1wb3J0IHsgSG9tZSwgQmFyQ2hhcnQzLCBNZXNzYWdlQ2lyY2xlLCBWaWRlbywgU2V0dGluZ3MsIENyZWRpdENhcmQsIERvbGxhclNpZ24sIEhlbHBDaXJjbGUsIExvZ091dCwgVXNlciwgQ3Jvd24gfSBmcm9tICdsdWNpZGUtcmVhY3QnO1xuXG5pbnRlcmZhY2UgU2lkZWJhckxheW91dFByb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbn1cblxuY29uc3QgU2lkZWJhckxheW91dDogUmVhY3QuRkM8U2lkZWJhckxheW91dFByb3BzPiA9ICh7IGNoaWxkcmVuIH0pID0+IHtcbiAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKCk7XG4gIGNvbnN0IFtob3ZlcmVkSXRlbSwgc2V0SG92ZXJlZEl0ZW1dID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtzaG93QWNjb3VudE1lbnUsIHNldFNob3dBY2NvdW50TWVudV0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IGFjY291bnRNZW51UmVmID0gdXNlUmVmPEhUTUxEaXZFbGVtZW50PihudWxsKTtcblxuICAvLyBDbG9zZSBtZW51IHdoZW4gY2xpY2tpbmcgb3V0c2lkZVxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IGhhbmRsZUNsaWNrT3V0c2lkZSA9IChldmVudDogTW91c2VFdmVudCkgPT4ge1xuICAgICAgaWYgKGFjY291bnRNZW51UmVmLmN1cnJlbnQgJiYgIWFjY291bnRNZW51UmVmLmN1cnJlbnQuY29udGFpbnMoZXZlbnQudGFyZ2V0IGFzIE5vZGUpKSB7XG4gICAgICAgIHNldFNob3dBY2NvdW50TWVudShmYWxzZSk7XG4gICAgICB9XG4gICAgfTtcblxuICAgIGRvY3VtZW50LmFkZEV2ZW50TGlzdGVuZXIoJ21vdXNlZG93bicsIGhhbmRsZUNsaWNrT3V0c2lkZSk7XG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIGRvY3VtZW50LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ21vdXNlZG93bicsIGhhbmRsZUNsaWNrT3V0c2lkZSk7XG4gICAgfTtcbiAgfSwgW10pO1xuXG4gIC8vIFdhcm0gSW50ZWxsaWdlbmNlIFRoZW1lIC0gRm9jdXNlZCwgSW50ZWxsaWdlbnQsIFdhcm1cbiAgY29uc3QgY29sb3JzID0ge1xuICAgIHByaW1hcnk6ICcjRkY2QjM1JywgICAgICAgIC8vIFdhcm0gY29yYWwtb3JhbmdlXG4gICAgcHJpbWFyeUxpZ2h0OiAnI0ZGOEE2NScsICAgLy8gU29mdCB3YXJtIG9yYW5nZVxuICAgIHByaW1hcnlEYXJrOiAnI0U2NTEwMCcsICAgIC8vIERlZXAgd2FybSBvcmFuZ2VcbiAgICBhY2NlbnQ6ICcjRkZGM0UwJywgICAgICAgICAvLyBXYXJtIGNyZWFtXG4gICAgc3VyZmFjZTogJyNGRUZFRkUnLCAgICAgICAgLy8gV2FybSB3aGl0ZVxuICAgIHN1cmZhY2VFbGV2YXRlZDogJyNGRkZGRkYnLCAvLyBQdXJlIHdoaXRlXG4gICAgYmFja2dyb3VuZDogJyNGRkY4RjMnLCAgICAgLy8gV2FybSBiYWNrZ3JvdW5kXG4gICAgd2FybUdsb3c6ICcjRkZFMEIyJywgICAgICAgLy8gU3VidGxlIHdhcm0gZ2xvd1xuICAgIHRleHQ6IHtcbiAgICAgIHByaW1hcnk6ICcjMkQxQjE0JywgICAgICAvLyBXYXJtIGRhcmsgYnJvd25cbiAgICAgIHNlY29uZGFyeTogJyM1RDQwMzcnLCAgICAvLyBNZWRpdW0gd2FybSBicm93blxuICAgICAgdGVydGlhcnk6ICcjOEQ2RTYzJywgICAgIC8vIExpZ2h0IHdhcm0gYnJvd25cbiAgICAgIG11dGVkOiAnI0JDQUFBNCcsICAgICAgICAvLyBXYXJtIGdyYXlcbiAgICAgIGludmVyc2U6ICcjRkZGRkZGJyAgICAgICAvLyBXaGl0ZSB0ZXh0XG4gICAgfSxcbiAgICBib3JkZXI6IHtcbiAgICAgIGxpZ2h0OiAnI0Y1RTZEMycsICAgICAgICAvLyBXYXJtIGxpZ2h0IGJvcmRlclxuICAgICAgbWVkaXVtOiAnI0UxQzRBMCcsICAgICAgIC8vIFdhcm0gbWVkaXVtIGJvcmRlclxuICAgICAgcHJpbWFyeTogJyNGRjZCMzUnICAgICAgIC8vIFByaW1hcnkgYm9yZGVyXG4gICAgfSxcbiAgICBzaWRlYmFyOiB7XG4gICAgICBiYWNrZ3JvdW5kOiAnbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI0ZGNkIzNSAwJSwgI0ZGOEE2NSA1MCUsICNGRkI3NEQgMTAwJSknLFxuICAgICAgYmFja2dyb3VuZFNvbGlkOiAnI0ZGNkIzNScsXG4gICAgICB0ZXh0OiAnI0ZGRkZGRicsXG4gICAgICB0ZXh0U2Vjb25kYXJ5OiAncmdiYSgyNTUsIDI1NSwgMjU1LCAwLjkpJyxcbiAgICAgIHRleHRUZXJ0aWFyeTogJ3JnYmEoMjU1LCAyNTUsIDI1NSwgMC43KScsXG4gICAgICBob3ZlcjogJ3JnYmEoMjU1LCAyNTUsIDI1NSwgMC4xNSknLFxuICAgICAgYWN0aXZlOiAncmdiYSgyNTUsIDI1NSwgMjU1LCAwLjI1KScsXG4gICAgICBib3JkZXI6ICdyZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMiknLFxuICAgICAgZ2xvdzogJ3JnYmEoMjU1LCAxMDcsIDUzLCAwLjMpJ1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBtZW51SXRlbXMgPSBbXG4gICAge1xuICAgICAgaHJlZjogJy8nLFxuICAgICAgbGFiZWw6ICdCcmllZmluZyBSb29tJyxcbiAgICAgIGljb246IEhvbWUsXG4gICAgICBkZXNjcmlwdGlvbjogJ01pc3Npb24gQ29udHJvbCdcbiAgICB9LFxuICAgIHtcbiAgICAgIGhyZWY6ICcvdHdlZXQtY2VudGVyJyxcbiAgICAgIGxhYmVsOiAnRHJhZnRpbmcgRGVzaycsXG4gICAgICBpY29uOiBNZXNzYWdlQ2lyY2xlLFxuICAgICAgZGVzY3JpcHRpb246ICdBSSBXcml0aW5nJ1xuICAgIH0sXG4gICAge1xuICAgICAgaHJlZjogJy9kYXNoYm9hcmQnLFxuICAgICAgbGFiZWw6ICdHcm93dGggTGFiJyxcbiAgICAgIGljb246IEJhckNoYXJ0MyxcbiAgICAgIGRlc2NyaXB0aW9uOiAnQW5hbHl0aWNzJ1xuICAgIH0sXG4gICAge1xuICAgICAgaHJlZjogJy9tZWV0aW5nJyxcbiAgICAgIGxhYmVsOiAnQUkgTWVldGluZ3MnLFxuICAgICAgaWNvbjogVmlkZW8sXG4gICAgICBkZXNjcmlwdGlvbjogJ1ZpZGVvIENhbGxzJ1xuICAgIH1cbiAgXTtcblxuICBjb25zdCBpc0FjdGl2ZSA9IChocmVmOiBzdHJpbmcpID0+IHtcbiAgICBpZiAoaHJlZiA9PT0gJy8nKSB7XG4gICAgICByZXR1cm4gcm91dGVyLnBhdGhuYW1lID09PSAnLyc7XG4gICAgfVxuICAgIHJldHVybiByb3V0ZXIucGF0aG5hbWUuc3RhcnRzV2l0aChocmVmKTtcbiAgfTtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgIGRpc3BsYXk6ICdmbGV4JyxcbiAgICAgIG1pbkhlaWdodDogJzEwMHZoJyxcbiAgICAgIGJhY2tncm91bmQ6IGBcbiAgICAgICAgcmFkaWFsLWdyYWRpZW50KGNpcmNsZSBhdCAyMCUgMjAlLCAke2NvbG9ycy5wcmltYXJ5fTE1IDAlLCB0cmFuc3BhcmVudCA1MCUpLFxuICAgICAgICByYWRpYWwtZ3JhZGllbnQoY2lyY2xlIGF0IDgwJSA4MCUsICR7Y29sb3JzLnByaW1hcnlMaWdodH0xMCAwJSwgdHJhbnNwYXJlbnQgNTAlKSxcbiAgICAgICAgbGluZWFyLWdyYWRpZW50KDEzNWRlZywgJHtjb2xvcnMucHJpbWFyeX0gMCUsICR7Y29sb3JzLnByaW1hcnlMaWdodH0gMTAwJSlcbiAgICAgIGAsXG4gICAgICBwYWRkaW5nOiAnMjBweCcsXG4gICAgICBnYXA6ICcyMHB4J1xuICAgIH19PlxuICAgICAgey8qIENsZWFuIFdhcm0gU2lkZWJhciAqL31cbiAgICAgIDxhc2lkZSBzdHlsZT17e1xuICAgICAgICB3aWR0aDogJzIwMHB4JyxcbiAgICAgICAgYmFja2dyb3VuZDogY29sb3JzLnNpZGViYXIuYmFja2dyb3VuZCxcbiAgICAgICAgbWluSGVpZ2h0OiAnY2FsYygxMDB2aCAtIDQwcHgpJyxcbiAgICAgICAgcG9zaXRpb246ICdyZWxhdGl2ZScsXG4gICAgICAgIGRpc3BsYXk6ICdmbGV4JyxcbiAgICAgICAgZmxleERpcmVjdGlvbjogJ2NvbHVtbicsXG4gICAgICAgIGJvcmRlclJhZGl1czogJzE2cHgnLFxuICAgICAgICBib3hTaGFkb3c6IGBcbiAgICAgICAgICAwIDIwcHggNjBweCAke2NvbG9ycy5zaWRlYmFyLmdsb3d9LFxuICAgICAgICAgIDAgOHB4IDMycHggcmdiYSgwLCAwLCAwLCAwLjE1KSxcbiAgICAgICAgICBpbnNldCAwIDFweCAwIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4yKVxuICAgICAgICBgLFxuICAgICAgICBvdmVyZmxvdzogJ2hpZGRlbicsXG4gICAgICAgIGJhY2tkcm9wRmlsdGVyOiAnYmx1cigxMHB4KScsXG4gICAgICAgIGJvcmRlcjogYDFweCBzb2xpZCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSlgXG4gICAgICB9fT5cbiAgICAgICAgey8qIENsZWFuIEJyYW5kIEhlYWRlciAqL31cbiAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgIHBhZGRpbmc6ICcyNHB4JyxcbiAgICAgICAgICBib3JkZXJCb3R0b206IGAxcHggc29saWQgJHtjb2xvcnMuc2lkZWJhci5ib3JkZXJ9YCxcbiAgICAgICAgICBkaXNwbGF5OiAnZmxleCcsXG4gICAgICAgICAganVzdGlmeUNvbnRlbnQ6ICdjZW50ZXInLFxuICAgICAgICAgIGFsaWduSXRlbXM6ICdjZW50ZXInXG4gICAgICAgIH19PlxuICAgICAgICAgIHsvKiBDbGVhbiBlbGVnYW50IGN1cnNpdmUgRSAqL31cbiAgICAgICAgICA8c3BhbiBzdHlsZT17e1xuICAgICAgICAgICAgZm9udFNpemU6ICczMnB4JyxcbiAgICAgICAgICAgIGNvbG9yOiBjb2xvcnMuc2lkZWJhci50ZXh0LFxuICAgICAgICAgICAgZm9udFdlaWdodDogJzQwMCcsXG4gICAgICAgICAgICBmb250RmFtaWx5OiAnR2VvcmdpYSwgc2VyaWYnLFxuICAgICAgICAgICAgZm9udFN0eWxlOiAnaXRhbGljJyxcbiAgICAgICAgICAgIHRleHRTaGFkb3c6ICcwIDJweCA4cHggcmdiYSgwLCAwLCAwLCAwLjMpJyxcbiAgICAgICAgICAgIGxldHRlclNwYWNpbmc6ICctMXB4J1xuICAgICAgICAgIH19PlxuICAgICAgICAgICAg4oSwXG4gICAgICAgICAgPC9zcGFuPlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogQ2xlYW4gTmF2aWdhdGlvbiAqL31cbiAgICAgICAgPG5hdiBzdHlsZT17eyBmbGV4OiAxLCBwYWRkaW5nOiAnMjBweCAwJywgb3ZlcmZsb3c6ICdhdXRvJyB9fT5cbiAgICAgICAgICA8ZGl2IHN0eWxlPXt7IHBhZGRpbmc6ICcwIDE2cHgnIH19PlxuICAgICAgICAgICAge21lbnVJdGVtcy5tYXAoKGl0ZW0sIGluZGV4KSA9PiB7XG4gICAgICAgICAgICAgIGNvbnN0IGFjdGl2ZSA9IGlzQWN0aXZlKGl0ZW0uaHJlZik7XG4gICAgICAgICAgICAgIGNvbnN0IGhvdmVyZWQgPSBob3ZlcmVkSXRlbSA9PT0gaXRlbS5ocmVmO1xuXG4gICAgICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICAgICAgPGRpdiBrZXk9e2l0ZW0uaHJlZn0gc3R5bGU9e3sgbWFyZ2luQm90dG9tOiAnNnB4JyB9fT5cbiAgICAgICAgICAgICAgICAgIDxMaW5rXG4gICAgICAgICAgICAgICAgICAgIGhyZWY9e2l0ZW0uaHJlZn1cbiAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3sgdGV4dERlY29yYXRpb246ICdub25lJyB9fVxuICAgICAgICAgICAgICAgICAgICBvbk1vdXNlRW50ZXI9eygpID0+IHNldEhvdmVyZWRJdGVtKGl0ZW0uaHJlZil9XG4gICAgICAgICAgICAgICAgICAgIG9uTW91c2VMZWF2ZT17KCkgPT4gc2V0SG92ZXJlZEl0ZW0obnVsbCl9XG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgICAgICBkaXNwbGF5OiAnZmxleCcsXG4gICAgICAgICAgICAgICAgICAgICAgYWxpZ25JdGVtczogJ2NlbnRlcicsXG4gICAgICAgICAgICAgICAgICAgICAgcGFkZGluZzogJzEycHggMTZweCcsXG4gICAgICAgICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnMTJweCcsXG4gICAgICAgICAgICAgICAgICAgICAgdHJhbnNpdGlvbjogJ2FsbCAwLjJzIGVhc2UnLFxuICAgICAgICAgICAgICAgICAgICAgIGN1cnNvcjogJ3BvaW50ZXInLFxuICAgICAgICAgICAgICAgICAgICAgIHBvc2l0aW9uOiAncmVsYXRpdmUnLFxuICAgICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6IGFjdGl2ZVxuICAgICAgICAgICAgICAgICAgICAgICAgPyBgbGluZWFyLWdyYWRpZW50KDEzNWRlZywgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjI1KSAwJSwgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpIDEwMCUpYFxuICAgICAgICAgICAgICAgICAgICAgICAgOiBob3ZlcmVkXG4gICAgICAgICAgICAgICAgICAgICAgICAgID8gYGxpbmVhci1ncmFkaWVudCgxMzVkZWcsIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xNSkgMCUsIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4wNSkgMTAwJSlgXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDogJ3RyYW5zcGFyZW50JyxcbiAgICAgICAgICAgICAgICAgICAgICBiYWNrZHJvcEZpbHRlcjogKGFjdGl2ZSB8fCBob3ZlcmVkKSA/ICdibHVyKDEwcHgpJyA6ICdub25lJyxcbiAgICAgICAgICAgICAgICAgICAgICBib3JkZXI6IGFjdGl2ZVxuICAgICAgICAgICAgICAgICAgICAgICAgPyAnMXB4IHNvbGlkIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4zKSdcbiAgICAgICAgICAgICAgICAgICAgICAgIDogJzFweCBzb2xpZCB0cmFuc3BhcmVudCcsXG4gICAgICAgICAgICAgICAgICAgICAgYm94U2hhZG93OiBhY3RpdmVcbiAgICAgICAgICAgICAgICAgICAgICAgID8gYDAgNHB4IDE2cHggcmdiYSgwLCAwLCAwLCAwLjEpLCBpbnNldCAwIDFweCAwIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC40KWBcbiAgICAgICAgICAgICAgICAgICAgICAgIDogJ25vbmUnXG4gICAgICAgICAgICAgICAgICAgIH19PlxuICAgICAgICAgICAgICAgICAgICAgIHsvKiBBY3RpdmUgaW5kaWNhdG9yICovfVxuICAgICAgICAgICAgICAgICAgICAgIHthY3RpdmUgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgICAgICAgICBwb3NpdGlvbjogJ2Fic29sdXRlJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgbGVmdDogJy0xcHgnLFxuICAgICAgICAgICAgICAgICAgICAgICAgICB0b3A6ICc1MCUnLFxuICAgICAgICAgICAgICAgICAgICAgICAgICB0cmFuc2Zvcm06ICd0cmFuc2xhdGVZKC01MCUpJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgd2lkdGg6ICczcHgnLFxuICAgICAgICAgICAgICAgICAgICAgICAgICBoZWlnaHQ6ICcyMHB4JyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZDogYGxpbmVhci1ncmFkaWVudCgxODBkZWcsIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC45KSAwJSwgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjYpIDEwMCUpYCxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnMCA2cHggNnB4IDAnLFxuICAgICAgICAgICAgICAgICAgICAgICAgICBib3hTaGFkb3c6ICcwIDAgOHB4IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC41KSdcbiAgICAgICAgICAgICAgICAgICAgICAgIH19IC8+XG4gICAgICAgICAgICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgICAgICAgICAgIHsvKiBJY29uIGNvbnRhaW5lciAqL31cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgICAgICAgICB3aWR0aDogJzI0cHgnLFxuICAgICAgICAgICAgICAgICAgICAgICAgaGVpZ2h0OiAnMjRweCcsXG4gICAgICAgICAgICAgICAgICAgICAgICBkaXNwbGF5OiAnZmxleCcsXG4gICAgICAgICAgICAgICAgICAgICAgICBhbGlnbkl0ZW1zOiAnY2VudGVyJyxcbiAgICAgICAgICAgICAgICAgICAgICAgIGp1c3RpZnlDb250ZW50OiAnY2VudGVyJyxcbiAgICAgICAgICAgICAgICAgICAgICAgIG1hcmdpblJpZ2h0OiAnMTJweCcsXG4gICAgICAgICAgICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6ICc2cHgnLFxuICAgICAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZDogYWN0aXZlXG4gICAgICAgICAgICAgICAgICAgICAgICAgID8gYGxpbmVhci1ncmFkaWVudCgxMzVkZWcsIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4zKSAwJSwgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpIDEwMCUpYFxuICAgICAgICAgICAgICAgICAgICAgICAgICA6ICd0cmFuc3BhcmVudCdcbiAgICAgICAgICAgICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxpdGVtLmljb25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgc2l6ZT17MTZ9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yPXtjb2xvcnMuc2lkZWJhci50ZXh0fVxuICAgICAgICAgICAgICAgICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZpbHRlcjogYWN0aXZlID8gJ2Ryb3Atc2hhZG93KDAgMXB4IDJweCByZ2JhKDAsIDAsIDAsIDAuMikpJyA6ICdub25lJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICAgIHsvKiBMYWJlbCAqL31cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgICAgICAgICBmb250U2l6ZTogJzE1cHgnLFxuICAgICAgICAgICAgICAgICAgICAgICAgZm9udFdlaWdodDogYWN0aXZlID8gJzYwMCcgOiAnNTAwJyxcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiBjb2xvcnMuc2lkZWJhci50ZXh0LFxuICAgICAgICAgICAgICAgICAgICAgICAgbGV0dGVyU3BhY2luZzogJy0wLjNweCcsXG4gICAgICAgICAgICAgICAgICAgICAgICB0ZXh0U2hhZG93OiBhY3RpdmUgPyAnMCAxcHggMnB4IHJnYmEoMCwgMCwgMCwgMC4xKScgOiAnbm9uZSdcbiAgICAgICAgICAgICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtpdGVtLmxhYmVsfVxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKTtcbiAgICAgICAgICAgIH0pfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L25hdj5cblxuICAgICAgICB7LyogQ29tcGFjdCBBY2NvdW50IFNlY3Rpb24gKi99XG4gICAgICAgIDxkaXZcbiAgICAgICAgICByZWY9e2FjY291bnRNZW51UmVmfVxuICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICBwYWRkaW5nOiAnMTZweCAxMnB4JyxcbiAgICAgICAgICAgIGJvcmRlclRvcDogYDFweCBzb2xpZCAke2NvbG9ycy5zaWRlYmFyLmJvcmRlcn1gLFxuICAgICAgICAgICAgbWFyZ2luVG9wOiAnYXV0bycsXG4gICAgICAgICAgICBiYWNrZ3JvdW5kOiBgcmFkaWFsLWdyYWRpZW50KGNpcmNsZSBhdCBjZW50ZXIgYm90dG9tLCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSkgMCUsIHRyYW5zcGFyZW50IDcwJSlgLFxuICAgICAgICAgICAgcG9zaXRpb246ICdyZWxhdGl2ZSdcbiAgICAgICAgICB9fVxuICAgICAgICA+XG4gICAgICAgICAgey8qIENvbXBhY3QgQWNjb3VudCBQcm9maWxlICovfVxuICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dBY2NvdW50TWVudSghc2hvd0FjY291bnRNZW51KX1cbiAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgIGRpc3BsYXk6ICdmbGV4JyxcbiAgICAgICAgICAgICAgYWxpZ25JdGVtczogJ2NlbnRlcicsXG4gICAgICAgICAgICAgIGdhcDogJzhweCcsXG4gICAgICAgICAgICAgIHBhZGRpbmc6ICc4cHggMTJweCcsXG4gICAgICAgICAgICAgIGJhY2tncm91bmQ6IGBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMikgMCUsIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4wNSkgMTAwJSlgLFxuICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6ICc4cHgnLFxuICAgICAgICAgICAgICBjdXJzb3I6ICdwb2ludGVyJyxcbiAgICAgICAgICAgICAgdHJhbnNpdGlvbjogJ2FsbCAwLjJzIGVhc2UnLFxuICAgICAgICAgICAgICBiYWNrZHJvcEZpbHRlcjogJ2JsdXIoMTBweCknLFxuICAgICAgICAgICAgICBib3JkZXI6ICcxcHggc29saWQgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjIpJyxcbiAgICAgICAgICAgICAgYm94U2hhZG93OiBgMCAycHggOHB4IHJnYmEoMCwgMCwgMCwgMC4xKSwgaW5zZXQgMCAxcHggMCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMylgXG4gICAgICAgICAgICB9fVxuICAgICAgICAgID5cbiAgICAgICAgICAgIHsvKiBDb21wYWN0IEF2YXRhciAqL31cbiAgICAgICAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgICAgICAgd2lkdGg6ICcyNHB4JyxcbiAgICAgICAgICAgICAgaGVpZ2h0OiAnMjRweCcsXG4gICAgICAgICAgICAgIGJhY2tncm91bmQ6IGBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMykgMCUsIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xKSAxMDAlKWAsXG4gICAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzZweCcsXG4gICAgICAgICAgICAgIGRpc3BsYXk6ICdmbGV4JyxcbiAgICAgICAgICAgICAgYWxpZ25JdGVtczogJ2NlbnRlcicsXG4gICAgICAgICAgICAgIGp1c3RpZnlDb250ZW50OiAnY2VudGVyJyxcbiAgICAgICAgICAgICAgcG9zaXRpb246ICdyZWxhdGl2ZScsXG4gICAgICAgICAgICAgIGJhY2tkcm9wRmlsdGVyOiAnYmx1cigxMHB4KScsXG4gICAgICAgICAgICAgIGJvcmRlcjogJzFweCBzb2xpZCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMiknLFxuICAgICAgICAgICAgICBmbGV4U2hyaW5rOiAwXG4gICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAgPHNwYW4gc3R5bGU9e3tcbiAgICAgICAgICAgICAgICBjb2xvcjogY29sb3JzLnNpZGViYXIudGV4dCxcbiAgICAgICAgICAgICAgICBmb250U2l6ZTogJzExcHgnLFxuICAgICAgICAgICAgICAgIGZvbnRXZWlnaHQ6ICc2MDAnLFxuICAgICAgICAgICAgICAgIHRleHRTaGFkb3c6ICcwIDFweCAycHggcmdiYSgwLCAwLCAwLCAwLjEpJ1xuICAgICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAgICBBXG4gICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgey8qIE9ubGluZSBpbmRpY2F0b3IgKi99XG4gICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICBwb3NpdGlvbjogJ2Fic29sdXRlJyxcbiAgICAgICAgICAgICAgICBib3R0b206ICctMXB4JyxcbiAgICAgICAgICAgICAgICByaWdodDogJy0xcHgnLFxuICAgICAgICAgICAgICAgIHdpZHRoOiAnNnB4JyxcbiAgICAgICAgICAgICAgICBoZWlnaHQ6ICc2cHgnLFxuICAgICAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzUwJScsXG4gICAgICAgICAgICAgICAgYmFja2dyb3VuZDogYHJhZGlhbC1ncmFkaWVudChjaXJjbGUsICMwMEU2NzYgMCUsICMwMEM4NTMgMTAwJSlgLFxuICAgICAgICAgICAgICAgIGJvcmRlcjogJzFweCBzb2xpZCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuOSknXG4gICAgICAgICAgICAgIH19IC8+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIENvbXBhY3QgVXNlciBJbmZvICovfVxuICAgICAgICAgICAgPGRpdiBzdHlsZT17eyBmbGV4OiAxLCBtaW5XaWR0aDogMCB9fT5cbiAgICAgICAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnMTJweCcsXG4gICAgICAgICAgICAgICAgZm9udFdlaWdodDogJzYwMCcsXG4gICAgICAgICAgICAgICAgY29sb3I6IGNvbG9ycy5zaWRlYmFyLnRleHQsXG4gICAgICAgICAgICAgICAgbGluZUhlaWdodDogJzEuMicsXG4gICAgICAgICAgICAgICAgbWFyZ2luQm90dG9tOiAnMXB4JyxcbiAgICAgICAgICAgICAgICB0ZXh0U2hhZG93OiAnMCAxcHggMnB4IHJnYmEoMCwgMCwgMCwgMC4xKScsXG4gICAgICAgICAgICAgICAgb3ZlcmZsb3c6ICdoaWRkZW4nLFxuICAgICAgICAgICAgICAgIHRleHRPdmVyZmxvdzogJ2VsbGlwc2lzJyxcbiAgICAgICAgICAgICAgICB3aGl0ZVNwYWNlOiAnbm93cmFwJ1xuICAgICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAgICBBbGV4IENoZW5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICBmb250U2l6ZTogJzEwcHgnLFxuICAgICAgICAgICAgICAgIGNvbG9yOiBjb2xvcnMuc2lkZWJhci50ZXh0VGVydGlhcnksXG4gICAgICAgICAgICAgICAgbGluZUhlaWdodDogJzEuMicsXG4gICAgICAgICAgICAgICAgZm9udFdlaWdodDogJzUwMCdcbiAgICAgICAgICAgICAgfX0+XG4gICAgICAgICAgICAgICAgQUkgTWFuYWdlclxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7LyogRHJvcGRvd24gaW5kaWNhdG9yICovfVxuICAgICAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgICAgICB3aWR0aDogJzEycHgnLFxuICAgICAgICAgICAgICBoZWlnaHQ6ICcxMnB4JyxcbiAgICAgICAgICAgICAgZGlzcGxheTogJ2ZsZXgnLFxuICAgICAgICAgICAgICBhbGlnbkl0ZW1zOiAnY2VudGVyJyxcbiAgICAgICAgICAgICAganVzdGlmeUNvbnRlbnQ6ICdjZW50ZXInLFxuICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6ICczcHgnLFxuICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiBgbGluZWFyLWdyYWRpZW50KDEzNWRlZywgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjIpIDAlLCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMDUpIDEwMCUpYCxcbiAgICAgICAgICAgICAgZmxleFNocmluazogMFxuICAgICAgICAgICAgfX0+XG4gICAgICAgICAgICAgIDxzcGFuIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgZm9udFNpemU6ICc4cHgnLFxuICAgICAgICAgICAgICAgIGNvbG9yOiBjb2xvcnMuc2lkZWJhci50ZXh0U2Vjb25kYXJ5LFxuICAgICAgICAgICAgICAgIHRyYW5zZm9ybTogc2hvd0FjY291bnRNZW51ID8gJ3JvdGF0ZSgxODBkZWcpJyA6ICdyb3RhdGUoMGRlZyknLFxuICAgICAgICAgICAgICAgIHRyYW5zaXRpb246ICd0cmFuc2Zvcm0gMC4ycyBlYXNlJ1xuICAgICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAgICDijIRcbiAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogUHJlbWl1bSBBY2NvdW50IE1lbnUgUG9wdXAgKi99XG4gICAgICAgICAge3Nob3dBY2NvdW50TWVudSAmJiAoXG4gICAgICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgICAgIHBvc2l0aW9uOiAnYWJzb2x1dGUnLFxuICAgICAgICAgICAgICBib3R0b206ICcxMDAlJyxcbiAgICAgICAgICAgICAgbGVmdDogJzZweCcsXG4gICAgICAgICAgICAgIHJpZ2h0OiAnNnB4JyxcbiAgICAgICAgICAgICAgbWFyZ2luQm90dG9tOiAnMTJweCcsXG4gICAgICAgICAgICAgIGJhY2tncm91bmQ6IGBcbiAgICAgICAgICAgICAgICBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLFxuICAgICAgICAgICAgICAgICAgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjk1KSAwJSxcbiAgICAgICAgICAgICAgICAgIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC45KSA1MCUsXG4gICAgICAgICAgICAgICAgICByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuOTUpIDEwMCVcbiAgICAgICAgICAgICAgICApXG4gICAgICAgICAgICAgIGAsXG4gICAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzE2cHgnLFxuICAgICAgICAgICAgICBib3hTaGFkb3c6IGBcbiAgICAgICAgICAgICAgICAwIDIwcHggNjBweCByZ2JhKDAsIDAsIDAsIDAuMjUpLFxuICAgICAgICAgICAgICAgIDAgOHB4IDMycHggcmdiYSgwLCAwLCAwLCAwLjE1KSxcbiAgICAgICAgICAgICAgICAwIDRweCAxNnB4IHJnYmEoMjU1LCAxMDcsIDUzLCAwLjIpLFxuICAgICAgICAgICAgICAgIGluc2V0IDAgMXB4IDAgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjgpLFxuICAgICAgICAgICAgICAgIGluc2V0IDAgLTFweCAwIHJnYmEoMjU1LCAxMDcsIDUzLCAwLjEpXG4gICAgICAgICAgICAgIGAsXG4gICAgICAgICAgICAgIGJvcmRlcjogYDFweCBzb2xpZCByZ2JhKDI1NSwgMTA3LCA1MywgMC4yKWAsXG4gICAgICAgICAgICAgIG92ZXJmbG93OiAnaGlkZGVuJyxcbiAgICAgICAgICAgICAgekluZGV4OiAxMDAwLFxuICAgICAgICAgICAgICBiYWNrZHJvcEZpbHRlcjogJ2JsdXIoMjBweCknLFxuICAgICAgICAgICAgICBhbmltYXRpb246ICdzbGlkZVVwIDAuMnMgZWFzZS1vdXQnXG4gICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAgey8qIFByZW1pdW0gSGVhZGVyICovfVxuICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgcGFkZGluZzogJzE2cHggMTZweCAxMnB4IDE2cHgnLFxuICAgICAgICAgICAgICAgIGJvcmRlckJvdHRvbTogYDFweCBzb2xpZCByZ2JhKDI1NSwgMTA3LCA1MywgMC4xNSlgLFxuICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6IGBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCByZ2JhKDI1NSwgMTA3LCA1MywgMC4xKSAwJSwgcmdiYSgyNTUsIDEzOCwgMTAxLCAwLjA1KSAxMDAlKWBcbiAgICAgICAgICAgICAgfX0+XG4gICAgICAgICAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgZGlzcGxheTogJ2ZsZXgnLFxuICAgICAgICAgICAgICAgICAgYWxpZ25JdGVtczogJ2NlbnRlcicsXG4gICAgICAgICAgICAgICAgICBnYXA6ICcxMHB4J1xuICAgICAgICAgICAgICAgIH19PlxuICAgICAgICAgICAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgICB3aWR0aDogJzMycHgnLFxuICAgICAgICAgICAgICAgICAgICBoZWlnaHQ6ICczMnB4JyxcbiAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZDogYGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICR7Y29sb3JzLnByaW1hcnl9IDAlLCAke2NvbG9ycy5wcmltYXJ5TGlnaHR9IDEwMCUpYCxcbiAgICAgICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnOHB4JyxcbiAgICAgICAgICAgICAgICAgICAgZGlzcGxheTogJ2ZsZXgnLFxuICAgICAgICAgICAgICAgICAgICBhbGlnbkl0ZW1zOiAnY2VudGVyJyxcbiAgICAgICAgICAgICAgICAgICAganVzdGlmeUNvbnRlbnQ6ICdjZW50ZXInLFxuICAgICAgICAgICAgICAgICAgICBib3hTaGFkb3c6ICcwIDRweCAxMnB4IHJnYmEoMjU1LCAxMDcsIDUzLCAwLjMpJyxcbiAgICAgICAgICAgICAgICAgICAgcG9zaXRpb246ICdyZWxhdGl2ZSdcbiAgICAgICAgICAgICAgICAgIH19PlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiAnd2hpdGUnLFxuICAgICAgICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnMTRweCcsXG4gICAgICAgICAgICAgICAgICAgICAgZm9udFdlaWdodDogJzcwMCcsXG4gICAgICAgICAgICAgICAgICAgICAgdGV4dFNoYWRvdzogJzAgMXB4IDJweCByZ2JhKDAsIDAsIDAsIDAuMiknXG4gICAgICAgICAgICAgICAgICAgIH19PlxuICAgICAgICAgICAgICAgICAgICAgIEFcbiAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8Q3Jvd25cbiAgICAgICAgICAgICAgICAgICAgICBzaXplPXsxMH1cbiAgICAgICAgICAgICAgICAgICAgICBjb2xvcj1cImdvbGRcIlxuICAgICAgICAgICAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgICAgICAgICBwb3NpdGlvbjogJ2Fic29sdXRlJyxcbiAgICAgICAgICAgICAgICAgICAgICAgIHRvcDogJy0ycHgnLFxuICAgICAgICAgICAgICAgICAgICAgICAgcmlnaHQ6ICctMnB4JyxcbiAgICAgICAgICAgICAgICAgICAgICAgIGZpbHRlcjogJ2Ryb3Atc2hhZG93KDAgMXB4IDJweCByZ2JhKDAsIDAsIDAsIDAuMykpJ1xuICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgICAgICBmb250U2l6ZTogJzE1cHgnLFxuICAgICAgICAgICAgICAgICAgICAgIGZvbnRXZWlnaHQ6ICc3MDAnLFxuICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiBjb2xvcnMudGV4dC5wcmltYXJ5LFxuICAgICAgICAgICAgICAgICAgICAgIGxpbmVIZWlnaHQ6ICcxLjInLFxuICAgICAgICAgICAgICAgICAgICAgIG1hcmdpbkJvdHRvbTogJzJweCdcbiAgICAgICAgICAgICAgICAgICAgfX0+XG4gICAgICAgICAgICAgICAgICAgICAgQWxleCBDaGVuXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgICAgICAgZm9udFNpemU6ICcxMnB4JyxcbiAgICAgICAgICAgICAgICAgICAgICBjb2xvcjogY29sb3JzLnRleHQuc2Vjb25kYXJ5LFxuICAgICAgICAgICAgICAgICAgICAgIGZvbnRXZWlnaHQ6ICc1MDAnLFxuICAgICAgICAgICAgICAgICAgICAgIGRpc3BsYXk6ICdmbGV4JyxcbiAgICAgICAgICAgICAgICAgICAgICBhbGlnbkl0ZW1zOiAnY2VudGVyJyxcbiAgICAgICAgICAgICAgICAgICAgICBnYXA6ICc0cHgnXG4gICAgICAgICAgICAgICAgICAgIH19PlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgICAgICAgIHdpZHRoOiAnNnB4JyxcbiAgICAgICAgICAgICAgICAgICAgICAgIGhlaWdodDogJzZweCcsXG4gICAgICAgICAgICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6ICc1MCUnLFxuICAgICAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZDogYGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICMwMEU2NzYgMCUsICMwMEM4NTMgMTAwJSlgLFxuICAgICAgICAgICAgICAgICAgICAgICAgYm94U2hhZG93OiAnMCAwIDRweCByZ2JhKDAsIDIzMCwgMTE4LCAwLjUpJ1xuICAgICAgICAgICAgICAgICAgICAgIH19IC8+XG4gICAgICAgICAgICAgICAgICAgICAgUHJvIE1lbWJlclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICB7LyogUHJlbWl1bSBNZW51IEl0ZW1zICovfVxuICAgICAgICAgICAgICB7W1xuICAgICAgICAgICAgICAgIHsgbGFiZWw6ICdBY2NvdW50IFNldHRpbmdzJywgaWNvbjogU2V0dGluZ3MsIGRlc2NyaXB0aW9uOiAnTWFuYWdlIHlvdXIgcHJvZmlsZScgfSxcbiAgICAgICAgICAgICAgICB7IGxhYmVsOiAnU3Vic2NyaXB0aW9uJywgaWNvbjogQ3Jvd24sIGRlc2NyaXB0aW9uOiAnUHJvIHBsYW4gZGV0YWlscycgfSxcbiAgICAgICAgICAgICAgICB7IGxhYmVsOiAnQmlsbGluZycsIGljb246IENyZWRpdENhcmQsIGRlc2NyaXB0aW9uOiAnUGF5bWVudCAmIGludm9pY2VzJyB9LFxuICAgICAgICAgICAgICAgIHsgbGFiZWw6ICdIZWxwICYgU3VwcG9ydCcsIGljb246IEhlbHBDaXJjbGUsIGRlc2NyaXB0aW9uOiAnMjQvNyBhc3Npc3RhbmNlJyB9LFxuICAgICAgICAgICAgICAgIHsgbGFiZWw6ICdTaWduIE91dCcsIGljb246IExvZ091dCwgZGVzY3JpcHRpb246ICdTZWN1cmUgbG9nb3V0JywgaXNEZXN0cnVjdGl2ZTogdHJ1ZSB9XG4gICAgICAgICAgICAgIF0ubWFwKChpdGVtLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICAgIGtleT17aW5kZXh9XG4gICAgICAgICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgICBwYWRkaW5nOiAnMTRweCAxNnB4JyxcbiAgICAgICAgICAgICAgICAgICAgY3Vyc29yOiAncG9pbnRlcicsXG4gICAgICAgICAgICAgICAgICAgIHRyYW5zaXRpb246ICdhbGwgMC4ycyBlYXNlJyxcbiAgICAgICAgICAgICAgICAgICAgYm9yZGVyQm90dG9tOiBpbmRleCA8IDQgPyBgMXB4IHNvbGlkIHJnYmEoMjU1LCAxMDcsIDUzLCAwLjA4KWAgOiAnbm9uZScsXG4gICAgICAgICAgICAgICAgICAgIGRpc3BsYXk6ICdmbGV4JyxcbiAgICAgICAgICAgICAgICAgICAgYWxpZ25JdGVtczogJ2NlbnRlcicsXG4gICAgICAgICAgICAgICAgICAgIGdhcDogJzEycHgnLFxuICAgICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiAndHJhbnNwYXJlbnQnLFxuICAgICAgICAgICAgICAgICAgICBwb3NpdGlvbjogJ3JlbGF0aXZlJ1xuICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgIG9uTW91c2VFbnRlcj17KGUpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgZS5jdXJyZW50VGFyZ2V0LnN0eWxlLmJhY2tncm91bmQgPSBpdGVtLmlzRGVzdHJ1Y3RpdmVcbiAgICAgICAgICAgICAgICAgICAgICA/IGBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCByZ2JhKDIzMSwgNzYsIDYwLCAwLjEpIDAlLCByZ2JhKDIzMSwgNzYsIDYwLCAwLjA1KSAxMDAlKWBcbiAgICAgICAgICAgICAgICAgICAgICA6IGBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCByZ2JhKDI1NSwgMTA3LCA1MywgMC4xNSkgMCUsIHJnYmEoMjU1LCAxMzgsIDEwMSwgMC4wOCkgMTAwJSlgO1xuICAgICAgICAgICAgICAgICAgICBlLmN1cnJlbnRUYXJnZXQuc3R5bGUudHJhbnNmb3JtID0gJ3RyYW5zbGF0ZVgoMnB4KSc7XG4gICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgb25Nb3VzZUxlYXZlPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICBlLmN1cnJlbnRUYXJnZXQuc3R5bGUuYmFja2dyb3VuZCA9ICd0cmFuc3BhcmVudCc7XG4gICAgICAgICAgICAgICAgICAgIGUuY3VycmVudFRhcmdldC5zdHlsZS50cmFuc2Zvcm0gPSAndHJhbnNsYXRlWCgwcHgpJztcbiAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgey8qIEljb24gQ29udGFpbmVyICovfVxuICAgICAgICAgICAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgICB3aWR0aDogJzM2cHgnLFxuICAgICAgICAgICAgICAgICAgICBoZWlnaHQ6ICczNnB4JyxcbiAgICAgICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnMTBweCcsXG4gICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6IGl0ZW0uaXNEZXN0cnVjdGl2ZVxuICAgICAgICAgICAgICAgICAgICAgID8gYGxpbmVhci1ncmFkaWVudCgxMzVkZWcsIHJnYmEoMjMxLCA3NiwgNjAsIDAuMTUpIDAlLCByZ2JhKDIzMSwgNzYsIDYwLCAwLjA4KSAxMDAlKWBcbiAgICAgICAgICAgICAgICAgICAgICA6IGBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCByZ2JhKDI1NSwgMTA3LCA1MywgMC4xNSkgMCUsIHJnYmEoMjU1LCAxMzgsIDEwMSwgMC4wOCkgMTAwJSlgLFxuICAgICAgICAgICAgICAgICAgICBkaXNwbGF5OiAnZmxleCcsXG4gICAgICAgICAgICAgICAgICAgIGFsaWduSXRlbXM6ICdjZW50ZXInLFxuICAgICAgICAgICAgICAgICAgICBqdXN0aWZ5Q29udGVudDogJ2NlbnRlcicsXG4gICAgICAgICAgICAgICAgICAgIGJvcmRlcjogYDFweCBzb2xpZCAke2l0ZW0uaXNEZXN0cnVjdGl2ZSA/ICdyZ2JhKDIzMSwgNzYsIDYwLCAwLjIpJyA6ICdyZ2JhKDI1NSwgMTA3LCA1MywgMC4yKSd9YCxcbiAgICAgICAgICAgICAgICAgICAgYm94U2hhZG93OiBgMCAycHggOHB4ICR7aXRlbS5pc0Rlc3RydWN0aXZlID8gJ3JnYmEoMjMxLCA3NiwgNjAsIDAuMSknIDogJ3JnYmEoMjU1LCAxMDcsIDUzLCAwLjEpJ31gLFxuICAgICAgICAgICAgICAgICAgICBmbGV4U2hyaW5rOiAwXG4gICAgICAgICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAgICAgICAgPGl0ZW0uaWNvblxuICAgICAgICAgICAgICAgICAgICAgIHNpemU9ezE2fVxuICAgICAgICAgICAgICAgICAgICAgIGNvbG9yPXtpdGVtLmlzRGVzdHJ1Y3RpdmUgPyAnI2U3NGMzYycgOiBjb2xvcnMucHJpbWFyeX1cbiAgICAgICAgICAgICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgICAgICAgZmlsdGVyOiBgZHJvcC1zaGFkb3coMCAxcHggMnB4ICR7aXRlbS5pc0Rlc3RydWN0aXZlID8gJ3JnYmEoMjMxLCA3NiwgNjAsIDAuMiknIDogJ3JnYmEoMjU1LCAxMDcsIDUzLCAwLjIpJ30pYFxuICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgey8qIENvbnRlbnQgKi99XG4gICAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGZsZXg6IDEgfX0+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgICAgICBmb250U2l6ZTogJzE0cHgnLFxuICAgICAgICAgICAgICAgICAgICAgIGZvbnRXZWlnaHQ6ICc2MDAnLFxuICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiBpdGVtLmlzRGVzdHJ1Y3RpdmUgPyAnI2U3NGMzYycgOiBjb2xvcnMudGV4dC5wcmltYXJ5LFxuICAgICAgICAgICAgICAgICAgICAgIGxpbmVIZWlnaHQ6ICcxLjMnLFxuICAgICAgICAgICAgICAgICAgICAgIG1hcmdpbkJvdHRvbTogJzJweCcsXG4gICAgICAgICAgICAgICAgICAgICAgdGV4dFNoYWRvdzogJzAgMXB4IDJweCByZ2JhKDAsIDAsIDAsIDAuMDUpJ1xuICAgICAgICAgICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAgICAgICAgICB7aXRlbS5sYWJlbH1cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgICAgICBmb250U2l6ZTogJzExcHgnLFxuICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiBjb2xvcnMudGV4dC50ZXJ0aWFyeSxcbiAgICAgICAgICAgICAgICAgICAgICBmb250V2VpZ2h0OiAnNTAwJyxcbiAgICAgICAgICAgICAgICAgICAgICBsaW5lSGVpZ2h0OiAnMS4yJ1xuICAgICAgICAgICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAgICAgICAgICB7aXRlbS5kZXNjcmlwdGlvbn1cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgey8qIFN1YnRsZSBhcnJvdyBpbmRpY2F0b3IgKi99XG4gICAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgICAgIHdpZHRoOiAnMjBweCcsXG4gICAgICAgICAgICAgICAgICAgIGhlaWdodDogJzIwcHgnLFxuICAgICAgICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6ICc2cHgnLFxuICAgICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiBgbGluZWFyLWdyYWRpZW50KDEzNWRlZywgcmdiYSgyNTUsIDEwNywgNTMsIDAuMSkgMCUsIHJnYmEoMjU1LCAxMzgsIDEwMSwgMC4wNSkgMTAwJSlgLFxuICAgICAgICAgICAgICAgICAgICBkaXNwbGF5OiAnZmxleCcsXG4gICAgICAgICAgICAgICAgICAgIGFsaWduSXRlbXM6ICdjZW50ZXInLFxuICAgICAgICAgICAgICAgICAgICBqdXN0aWZ5Q29udGVudDogJ2NlbnRlcicsXG4gICAgICAgICAgICAgICAgICAgIG9wYWNpdHk6IDAuNixcbiAgICAgICAgICAgICAgICAgICAgdHJhbnNpdGlvbjogJ2FsbCAwLjJzIGVhc2UnXG4gICAgICAgICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgICAgICBmb250U2l6ZTogJzEwcHgnLFxuICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiBjb2xvcnMudGV4dC5zZWNvbmRhcnksXG4gICAgICAgICAgICAgICAgICAgICAgdHJhbnNmb3JtOiAncm90YXRlKC05MGRlZyknXG4gICAgICAgICAgICAgICAgICAgIH19PlxuICAgICAgICAgICAgICAgICAgICAgIOKMhFxuICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApfVxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvYXNpZGU+XG5cbiAgICAgIHsvKiBJbnRlbGxpZ2VudCBDb250ZW50IENhbnZhcyAqL31cbiAgICAgIDxtYWluIHN0eWxlPXt7XG4gICAgICAgIGZsZXhHcm93OiAxLFxuICAgICAgICBwb3NpdGlvbjogJ3JlbGF0aXZlJ1xuICAgICAgfX0+XG4gICAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6IGNvbG9ycy5zdXJmYWNlRWxldmF0ZWQsXG4gICAgICAgICAgYm9yZGVyUmFkaXVzOiAnMjBweCcsXG4gICAgICAgICAgbWluSGVpZ2h0OiAnY2FsYygxMDB2aCAtIDQwcHgpJyxcbiAgICAgICAgICBib3hTaGFkb3c6IGBcbiAgICAgICAgICAgIDAgMzJweCA4MHB4IHJnYmEoMCwgMCwgMCwgMC4xMiksXG4gICAgICAgICAgICAwIDhweCAzMnB4IHJnYmEoMCwgMCwgMCwgMC4wOCksXG4gICAgICAgICAgICBpbnNldCAwIDFweCAwIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC45KSxcbiAgICAgICAgICAgIDAgMCAwIDFweCByZ2JhKDI1NSwgMTA3LCA1MywgMC4xKVxuICAgICAgICAgIGAsXG4gICAgICAgICAgb3ZlcmZsb3c6ICdoaWRkZW4nLFxuICAgICAgICAgIHBvc2l0aW9uOiAncmVsYXRpdmUnLFxuICAgICAgICAgIGJhY2tkcm9wRmlsdGVyOiAnYmx1cigyMHB4KScsXG4gICAgICAgICAgYm9yZGVyOiAnMXB4IHNvbGlkIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4yKSdcbiAgICAgICAgfX0+XG4gICAgICAgICAgey8qIFN1YnRsZSB3YXJtIGFtYmllbnQgbGlnaHRpbmcgKi99XG4gICAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgICAgcG9zaXRpb246ICdhYnNvbHV0ZScsXG4gICAgICAgICAgICB0b3A6IDAsXG4gICAgICAgICAgICBsZWZ0OiAwLFxuICAgICAgICAgICAgcmlnaHQ6IDAsXG4gICAgICAgICAgICBoZWlnaHQ6ICcyMDBweCcsXG4gICAgICAgICAgICBiYWNrZ3JvdW5kOiBgXG4gICAgICAgICAgICAgIHJhZGlhbC1ncmFkaWVudChlbGxpcHNlIGF0IHRvcCwgJHtjb2xvcnMud2FybUdsb3d9MjAgMCUsIHRyYW5zcGFyZW50IDcwJSlcbiAgICAgICAgICAgIGAsXG4gICAgICAgICAgICBwb2ludGVyRXZlbnRzOiAnbm9uZSdcbiAgICAgICAgICB9fSAvPlxuXG4gICAgICAgICAgey8qIENvbnRlbnQgd2l0aCB3YXJtIGNvbnRleHQgKi99XG4gICAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgICAgcG9zaXRpb246ICdyZWxhdGl2ZScsXG4gICAgICAgICAgICB6SW5kZXg6IDEsXG4gICAgICAgICAgICBoZWlnaHQ6ICcxMDAlJ1xuICAgICAgICAgIH19PlxuICAgICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvbWFpbj5cbiAgICA8L2Rpdj5cbiAgKTtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IFNpZGViYXJMYXlvdXQ7Il0sIm5hbWVzIjpbIkxpbmsiLCJSZWFjdCIsInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlUmVmIiwidXNlUm91dGVyIiwiSG9tZSIsIkJhckNoYXJ0MyIsIk1lc3NhZ2VDaXJjbGUiLCJWaWRlbyIsIlNldHRpbmdzIiwiQ3JlZGl0Q2FyZCIsIkhlbHBDaXJjbGUiLCJMb2dPdXQiLCJDcm93biIsIlNpZGViYXJMYXlvdXQiLCJjaGlsZHJlbiIsInJvdXRlciIsImhvdmVyZWRJdGVtIiwic2V0SG92ZXJlZEl0ZW0iLCJzaG93QWNjb3VudE1lbnUiLCJzZXRTaG93QWNjb3VudE1lbnUiLCJhY2NvdW50TWVudVJlZiIsImhhbmRsZUNsaWNrT3V0c2lkZSIsImV2ZW50IiwiY3VycmVudCIsImNvbnRhaW5zIiwidGFyZ2V0IiwiZG9jdW1lbnQiLCJhZGRFdmVudExpc3RlbmVyIiwicmVtb3ZlRXZlbnRMaXN0ZW5lciIsImNvbG9ycyIsInByaW1hcnkiLCJwcmltYXJ5TGlnaHQiLCJwcmltYXJ5RGFyayIsImFjY2VudCIsInN1cmZhY2UiLCJzdXJmYWNlRWxldmF0ZWQiLCJiYWNrZ3JvdW5kIiwid2FybUdsb3ciLCJ0ZXh0Iiwic2Vjb25kYXJ5IiwidGVydGlhcnkiLCJtdXRlZCIsImludmVyc2UiLCJib3JkZXIiLCJsaWdodCIsIm1lZGl1bSIsInNpZGViYXIiLCJiYWNrZ3JvdW5kU29saWQiLCJ0ZXh0U2Vjb25kYXJ5IiwidGV4dFRlcnRpYXJ5IiwiaG92ZXIiLCJhY3RpdmUiLCJnbG93IiwibWVudUl0ZW1zIiwiaHJlZiIsImxhYmVsIiwiaWNvbiIsImRlc2NyaXB0aW9uIiwiaXNBY3RpdmUiLCJwYXRobmFtZSIsInN0YXJ0c1dpdGgiLCJkaXYiLCJzdHlsZSIsImRpc3BsYXkiLCJtaW5IZWlnaHQiLCJwYWRkaW5nIiwiZ2FwIiwiYXNpZGUiLCJ3aWR0aCIsInBvc2l0aW9uIiwiZmxleERpcmVjdGlvbiIsImJvcmRlclJhZGl1cyIsImJveFNoYWRvdyIsIm92ZXJmbG93IiwiYmFja2Ryb3BGaWx0ZXIiLCJib3JkZXJCb3R0b20iLCJqdXN0aWZ5Q29udGVudCIsImFsaWduSXRlbXMiLCJzcGFuIiwiZm9udFNpemUiLCJjb2xvciIsImZvbnRXZWlnaHQiLCJmb250RmFtaWx5IiwiZm9udFN0eWxlIiwidGV4dFNoYWRvdyIsImxldHRlclNwYWNpbmciLCJuYXYiLCJmbGV4IiwibWFwIiwiaXRlbSIsImluZGV4IiwiaG92ZXJlZCIsIm1hcmdpbkJvdHRvbSIsInRleHREZWNvcmF0aW9uIiwib25Nb3VzZUVudGVyIiwib25Nb3VzZUxlYXZlIiwidHJhbnNpdGlvbiIsImN1cnNvciIsImxlZnQiLCJ0b3AiLCJ0cmFuc2Zvcm0iLCJoZWlnaHQiLCJtYXJnaW5SaWdodCIsInNpemUiLCJmaWx0ZXIiLCJyZWYiLCJib3JkZXJUb3AiLCJtYXJnaW5Ub3AiLCJvbkNsaWNrIiwiZmxleFNocmluayIsImJvdHRvbSIsInJpZ2h0IiwibWluV2lkdGgiLCJsaW5lSGVpZ2h0IiwidGV4dE92ZXJmbG93Iiwid2hpdGVTcGFjZSIsInpJbmRleCIsImFuaW1hdGlvbiIsImlzRGVzdHJ1Y3RpdmUiLCJlIiwiY3VycmVudFRhcmdldCIsIm9wYWNpdHkiLCJtYWluIiwiZmxleEdyb3ciLCJiYWNrZ3JvdW5kQ29sb3IiLCJwb2ludGVyRXZlbnRzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./components/SidebarLayout.tsx\n");

/***/ }),

/***/ "./pages/_app.tsx":
/*!************************!*\
  !*** ./pages/_app.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/globals.css */ \"./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n\n // Global styles\nfunction MyApp({ Component, pageProps }) {\n    // Use the layout defined at the page level, if available\n    const getLayout = Component.getLayout || ((page)=>page);\n    return getLayout(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n        ...pageProps\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/_app.tsx\",\n        lineNumber: 18,\n        columnNumber: 20\n    }, this));\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MyApp);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy9fYXBwLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBK0IsQ0FBQyxnQkFBZ0I7QUFhaEQsU0FBU0EsTUFBTSxFQUFFQyxTQUFTLEVBQUVDLFNBQVMsRUFBc0I7SUFDekQseURBQXlEO0lBQ3pELE1BQU1DLFlBQVlGLFVBQVVFLFNBQVMsSUFBSyxFQUFDQyxPQUFTQSxJQUFHO0lBRXZELE9BQU9ELHdCQUFVLDhEQUFDRjtRQUFXLEdBQUdDLFNBQVM7Ozs7OztBQUMzQztBQUVBLGlFQUFlRixLQUFLQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZXhpZS1haS8uL3BhZ2VzL19hcHAudHN4PzJmYmUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICcuLi9zdHlsZXMvZ2xvYmFscy5jc3MnOyAvLyBHbG9iYWwgc3R5bGVzXG5pbXBvcnQgdHlwZSB7IEFwcFByb3BzIH0gZnJvbSAnbmV4dC9hcHAnO1xuaW1wb3J0IHR5cGUgeyBSZWFjdEVsZW1lbnQsIFJlYWN0Tm9kZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB0eXBlIHsgTmV4dFBhZ2UgfSBmcm9tICduZXh0JztcblxuZXhwb3J0IHR5cGUgTmV4dFBhZ2VXaXRoTGF5b3V0ID0gTmV4dFBhZ2UgJiB7XG4gIGdldExheW91dD86IChwYWdlOiBSZWFjdEVsZW1lbnQpID0+IFJlYWN0Tm9kZTtcbn07XG5cbmV4cG9ydCB0eXBlIEFwcFByb3BzV2l0aExheW91dCA9IEFwcFByb3BzICYge1xuICBDb21wb25lbnQ6IE5leHRQYWdlV2l0aExheW91dDtcbn07XG5cbmZ1bmN0aW9uIE15QXBwKHsgQ29tcG9uZW50LCBwYWdlUHJvcHMgfTogQXBwUHJvcHNXaXRoTGF5b3V0KSB7XG4gIC8vIFVzZSB0aGUgbGF5b3V0IGRlZmluZWQgYXQgdGhlIHBhZ2UgbGV2ZWwsIGlmIGF2YWlsYWJsZVxuICBjb25zdCBnZXRMYXlvdXQgPSBDb21wb25lbnQuZ2V0TGF5b3V0IHx8ICgocGFnZSkgPT4gcGFnZSk7XG5cbiAgcmV0dXJuIGdldExheW91dCg8Q29tcG9uZW50IHsuLi5wYWdlUHJvcHN9IC8+KTtcbn1cblxuZXhwb3J0IGRlZmF1bHQgTXlBcHA7Il0sIm5hbWVzIjpbIk15QXBwIiwiQ29tcG9uZW50IiwicGFnZVByb3BzIiwiZ2V0TGF5b3V0IiwicGFnZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./pages/_app.tsx\n");

/***/ }),

/***/ "./pages/tweet-center.tsx":
/*!********************************!*\
  !*** ./pages/tweet-center.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_SidebarLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/SidebarLayout */ \"./components/SidebarLayout.tsx\");\n// pages/tweet-center.tsx\n\n\n\nconst TweetCenterPage = ()=>{\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [aiSuggestion, setAiSuggestion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showSuggestion, setShowSuggestion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mode, setMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"manual\");\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const colors = {\n        primary: \"#FF6B35\",\n        primaryLight: \"#FF8A65\",\n        text: {\n            primary: \"#2D1B14\",\n            secondary: \"#5D4037\",\n            tertiary: \"#8D6E63\"\n        },\n        border: \"#F5E6D3\",\n        surface: \"#FFFFFF\",\n        warmGlow: \"#FFE0B2\"\n    };\n    // AI prediction simulation\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (content.length > 10 && mode === \"ai\") {\n            const timer = setTimeout(()=>{\n                // Simulate AI prediction based on content\n                if (content.includes(\"AI\")) {\n                    setAiSuggestion(\" can revolutionize your workflow and boost productivity by 10x\");\n                } else if (content.includes(\"productivity\")) {\n                    setAiSuggestion(\" tips that actually work: 1) Time blocking 2) AI automation 3) Single-tasking\");\n                } else if (content.includes(\"building\")) {\n                    setAiSuggestion(\" in public is the fastest way to grow your audience and get feedback\");\n                } else {\n                    setAiSuggestion(\" - here's what I learned from my experience\");\n                }\n                setShowSuggestion(true);\n            }, 500);\n            return ()=>clearTimeout(timer);\n        } else {\n            setShowSuggestion(false);\n        }\n    }, [\n        content,\n        mode\n    ]);\n    const handleTabPress = (e)=>{\n        if (e.key === \"Tab\" && showSuggestion) {\n            e.preventDefault();\n            setContent(content + aiSuggestion);\n            setShowSuggestion(false);\n            setAiSuggestion(\"\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"32px\",\n            height: \"100vh\",\n            overflow: \"auto\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    marginBottom: \"32px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        style: {\n                            color: colors.text.primary,\n                            margin: 0,\n                            fontSize: \"28px\",\n                            fontWeight: \"600\",\n                            letterSpacing: \"-0.5px\",\n                            marginBottom: \"8px\"\n                        },\n                        children: \"Drafting Desk\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        style: {\n                            color: colors.text.secondary,\n                            fontSize: \"16px\",\n                            margin: 0,\n                            fontWeight: \"400\",\n                            marginBottom: \"20px\"\n                        },\n                        children: \"Your intelligent writing companion\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"flex\",\n                            gap: \"4px\",\n                            padding: \"4px\",\n                            background: colors.surface,\n                            borderRadius: \"8px\",\n                            border: `1px solid ${colors.border}`,\n                            width: \"fit-content\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setMode(\"manual\"),\n                                style: {\n                                    padding: \"8px 16px\",\n                                    borderRadius: \"6px\",\n                                    border: \"none\",\n                                    background: mode === \"manual\" ? colors.primary : \"transparent\",\n                                    color: mode === \"manual\" ? \"white\" : colors.text.secondary,\n                                    fontSize: \"14px\",\n                                    fontWeight: \"500\",\n                                    cursor: \"pointer\",\n                                    transition: \"all 0.2s ease\"\n                                },\n                                children: \"Manual\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setMode(\"ai\"),\n                                style: {\n                                    padding: \"8px 16px\",\n                                    borderRadius: \"6px\",\n                                    border: \"none\",\n                                    background: mode === \"ai\" ? colors.primary : \"transparent\",\n                                    color: mode === \"ai\" ? \"white\" : colors.text.secondary,\n                                    fontSize: \"14px\",\n                                    fontWeight: \"500\",\n                                    cursor: \"pointer\",\n                                    transition: \"all 0.2s ease\"\n                                },\n                                children: \"AI Assist\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: \"grid\",\n                    gridTemplateColumns: \"240px 1fr 240px\",\n                    gap: \"20px\",\n                    height: \"calc(100vh - 180px)\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: colors.surface,\n                            borderRadius: \"12px\",\n                            padding: \"20px\",\n                            boxShadow: `0 2px 8px rgba(0, 0, 0, 0.04)`,\n                            border: `1px solid ${colors.border}`,\n                            overflow: \"auto\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    color: colors.text.primary,\n                                    margin: 0,\n                                    fontSize: \"16px\",\n                                    fontWeight: \"600\",\n                                    marginBottom: \"16px\"\n                                },\n                                children: \"Ideas & Hooks\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: \"20px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        style: {\n                                            color: colors.text.tertiary,\n                                            fontSize: \"12px\",\n                                            fontWeight: \"600\",\n                                            marginBottom: \"8px\",\n                                            textTransform: \"uppercase\",\n                                            letterSpacing: \"0.5px\"\n                                        },\n                                        children: \"Saved Ideas\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    [\n                                        \"AI productivity workflows\",\n                                        \"Building in public lessons\",\n                                        \"Remote work tips\",\n                                        \"Content creation process\"\n                                    ].map((idea, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                padding: \"8px 12px\",\n                                                background: `${colors.primary}05`,\n                                                borderRadius: \"6px\",\n                                                marginBottom: \"6px\",\n                                                cursor: \"pointer\",\n                                                transition: \"all 0.2s ease\",\n                                                border: `1px solid ${colors.primary}10`\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    color: colors.text.secondary,\n                                                    fontSize: \"13px\"\n                                                },\n                                                children: idea\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, index, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 15\n                                        }, undefined))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        style: {\n                                            color: colors.text.tertiary,\n                                            fontSize: \"12px\",\n                                            fontWeight: \"600\",\n                                            marginBottom: \"8px\",\n                                            textTransform: \"uppercase\",\n                                            letterSpacing: \"0.5px\"\n                                        },\n                                        children: \"Trending Hooks\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    [\n                                        \"Here's what I learned...\",\n                                        \"The biggest mistake I see...\",\n                                        \"3 things that changed my...\",\n                                        \"If I started over today...\"\n                                    ].map((hook, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                padding: \"8px 12px\",\n                                                background: `${colors.primary}08`,\n                                                borderRadius: \"6px\",\n                                                marginBottom: \"6px\",\n                                                cursor: \"pointer\",\n                                                transition: \"all 0.2s ease\",\n                                                border: `1px solid ${colors.primary}15`\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    color: colors.text.secondary,\n                                                    fontSize: \"13px\",\n                                                    fontStyle: \"italic\"\n                                                },\n                                                children: [\n                                                    '\"',\n                                                    hook,\n                                                    '\"'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, index, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 15\n                                        }, undefined))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: colors.surface,\n                            borderRadius: \"12px\",\n                            padding: \"24px\",\n                            boxShadow: `0 4px 16px rgba(0, 0, 0, 0.04)`,\n                            border: `1px solid ${colors.border}`,\n                            position: \"relative\",\n                            display: \"flex\",\n                            flexDirection: \"column\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: \"16px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        style: {\n                                            color: colors.text.primary,\n                                            margin: 0,\n                                            fontSize: \"18px\",\n                                            fontWeight: \"600\",\n                                            marginBottom: \"4px\"\n                                        },\n                                        children: mode === \"ai\" ? \"AI-Powered Writing\" : \"Manual Writing\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    mode === \"ai\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        style: {\n                                            color: colors.text.tertiary,\n                                            fontSize: \"13px\",\n                                            margin: 0,\n                                            fontWeight: \"400\"\n                                        },\n                                        children: \"Start typing and press Tab to accept AI suggestions\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    flex: 1,\n                                    position: \"relative\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        ref: textareaRef,\n                                        value: content,\n                                        onChange: (e)=>setContent(e.target.value),\n                                        onKeyDown: handleTabPress,\n                                        placeholder: mode === \"ai\" ? \"Start writing and I'll help you continue...\" : \"What's on your mind?\",\n                                        style: {\n                                            width: \"100%\",\n                                            height: \"100%\",\n                                            minHeight: \"320px\",\n                                            padding: \"20px\",\n                                            borderRadius: \"8px\",\n                                            background: \"#FEFEFE\",\n                                            fontSize: \"16px\",\n                                            lineHeight: \"1.6\",\n                                            color: colors.text.primary,\n                                            fontFamily: '-apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif',\n                                            resize: \"none\",\n                                            outline: \"none\",\n                                            boxShadow: `inset 0 1px 3px rgba(0, 0, 0, 0.06)`,\n                                            border: `1px solid ${colors.border}`,\n                                            transition: \"border-color 0.2s ease\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    showSuggestion && mode === \"ai\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            position: \"absolute\",\n                                            bottom: \"24px\",\n                                            left: \"24px\",\n                                            right: \"24px\",\n                                            padding: \"12px 16px\",\n                                            background: colors.primary,\n                                            borderRadius: \"8px\",\n                                            color: \"white\",\n                                            fontSize: \"14px\",\n                                            boxShadow: `0 4px 12px ${colors.primary}40`,\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            gap: \"8px\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    flex: 1\n                                                },\n                                                children: aiSuggestion\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                lineNumber: 303,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    padding: \"2px 6px\",\n                                                    background: \"rgba(255, 255, 255, 0.2)\",\n                                                    borderRadius: \"4px\",\n                                                    fontSize: \"11px\",\n                                                    fontWeight: \"600\"\n                                                },\n                                                children: \"Tab\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    justifyContent: \"space-between\",\n                                    alignItems: \"center\",\n                                    marginTop: \"16px\",\n                                    paddingTop: \"16px\",\n                                    borderTop: `1px solid ${colors.border}`\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            color: colors.text.tertiary,\n                                            fontSize: \"13px\",\n                                            fontWeight: \"500\"\n                                        },\n                                        children: [\n                                            content.length,\n                                            \"/280 characters\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 328,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            gap: \"8px\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                style: {\n                                                    padding: \"8px 16px\",\n                                                    background: colors.surface,\n                                                    color: colors.text.primary,\n                                                    border: `1px solid ${colors.border}`,\n                                                    borderRadius: \"6px\",\n                                                    fontSize: \"13px\",\n                                                    fontWeight: \"500\",\n                                                    cursor: \"pointer\",\n                                                    transition: \"all 0.2s ease\"\n                                                },\n                                                children: \"Save Draft\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                lineNumber: 337,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                style: {\n                                                    padding: \"8px 16px\",\n                                                    background: colors.primary,\n                                                    color: \"white\",\n                                                    border: \"none\",\n                                                    borderRadius: \"6px\",\n                                                    fontSize: \"13px\",\n                                                    fontWeight: \"600\",\n                                                    cursor: \"pointer\",\n                                                    transition: \"all 0.2s ease\"\n                                                },\n                                                children: \"Post Tweet\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                lineNumber: 351,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 320,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: colors.surface,\n                            borderRadius: \"12px\",\n                            padding: \"20px\",\n                            boxShadow: `0 2px 8px rgba(0, 0, 0, 0.04)`,\n                            border: `1px solid ${colors.border}`,\n                            overflow: \"auto\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    color: colors.text.primary,\n                                    margin: 0,\n                                    fontSize: \"16px\",\n                                    fontWeight: \"600\",\n                                    marginBottom: \"16px\"\n                                },\n                                children: \"AI Mentor\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 377,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    background: `${colors.primary}08`,\n                                    borderRadius: \"8px\",\n                                    padding: \"16px\",\n                                    marginBottom: \"16px\",\n                                    border: `1px solid ${colors.primary}15`\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            color: colors.text.primary,\n                                            fontSize: \"13px\",\n                                            fontWeight: \"600\",\n                                            marginBottom: \"8px\"\n                                        },\n                                        children: \"Real-time Tip\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 395,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        style: {\n                                            color: colors.text.secondary,\n                                            fontSize: \"13px\",\n                                            margin: 0,\n                                            lineHeight: \"1.4\"\n                                        },\n                                        children: \"Try starting with a question or bold statement. Your audience loves content that makes them think or challenges their assumptions.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 403,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 388,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: \"16px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        style: {\n                                            color: colors.text.tertiary,\n                                            fontSize: \"12px\",\n                                            fontWeight: \"600\",\n                                            marginBottom: \"8px\",\n                                            textTransform: \"uppercase\",\n                                            letterSpacing: \"0.5px\"\n                                        },\n                                        children: \"Your Brand Tone\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 415,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    [\n                                        {\n                                            tone: \"Helpful\",\n                                            desc: \"Share actionable insights\"\n                                        },\n                                        {\n                                            tone: \"Authentic\",\n                                            desc: \"Be genuine and personal\"\n                                        },\n                                        {\n                                            tone: \"Inspiring\",\n                                            desc: \"Motivate and encourage\"\n                                        }\n                                    ].map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                padding: \"8px 12px\",\n                                                background: `${colors.primary}05`,\n                                                borderRadius: \"6px\",\n                                                marginBottom: \"6px\",\n                                                border: `1px solid ${colors.primary}10`\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        color: colors.text.primary,\n                                                        fontSize: \"13px\",\n                                                        fontWeight: \"600\",\n                                                        marginBottom: \"2px\"\n                                                    },\n                                                    children: item.tone\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                    lineNumber: 437,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        color: colors.text.tertiary,\n                                                        fontSize: \"11px\"\n                                                    },\n                                                    children: item.desc\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                    lineNumber: 445,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                            lineNumber: 430,\n                                            columnNumber: 15\n                                        }, undefined))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 414,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        style: {\n                                            color: colors.text.tertiary,\n                                            fontSize: \"12px\",\n                                            fontWeight: \"600\",\n                                            marginBottom: \"8px\",\n                                            textTransform: \"uppercase\",\n                                            letterSpacing: \"0.5px\"\n                                        },\n                                        children: \"What's Working\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 457,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    [\n                                        'Posts with \"AI\" get 3x engagement',\n                                        \"Questions drive 2x more replies\",\n                                        \"Behind-the-scenes content performs well\"\n                                    ].map((insight, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                padding: \"8px 12px\",\n                                                background: \"#F0F9FF\",\n                                                borderRadius: \"6px\",\n                                                marginBottom: \"6px\",\n                                                border: \"1px solid #E0F2FE\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    color: colors.text.secondary,\n                                                    fontSize: \"12px\"\n                                                },\n                                                children: insight\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                lineNumber: 479,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, index, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                            lineNumber: 472,\n                                            columnNumber: 15\n                                        }, undefined))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 456,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 369,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                lineNumber: 127,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n        lineNumber: 59,\n        columnNumber: 5\n    }, undefined);\n};\nTweetCenterPage.getLayout = function getLayout(page) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SidebarLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: page\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n        lineNumber: 496,\n        columnNumber: 5\n    }, this);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TweetCenterPage);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/tweet-center.tsx\n");

/***/ }),

/***/ "./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/lucide-react"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Ftweet-center&preferredRegion=&absolutePagePath=.%2Fpages%2Ftweet-center.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();