"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/tweet-center",{

/***/ "./pages/tweet-center.tsx":
/*!********************************!*\
  !*** ./pages/tweet-center.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_SidebarLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/SidebarLayout */ \"./components/SidebarLayout.tsx\");\n// pages/tweet-center.tsx\n\nvar _s = $RefreshSig$();\n\n\nconst TweetCenterPage = ()=>{\n    _s();\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [aiSuggestion, setAiSuggestion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showSuggestion, setShowSuggestion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [aiEnabled, setAiEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [formatMode, setFormatMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"single\");\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const colors = {\n        primary: \"#FF6B35\",\n        primaryLight: \"#FF8A65\",\n        text: {\n            primary: \"#2D1B14\",\n            secondary: \"#5D4037\",\n            tertiary: \"#8D6E63\"\n        },\n        border: \"#F5E6D3\",\n        surface: \"#FFFFFF\",\n        warmGlow: \"#FFE0B2\",\n        paper: \"#FEFEFE\"\n    };\n    // Intelligent AI prediction based on content context\n    const generateContextualSuggestion = (text)=>{\n        const lowerText = text.toLowerCase();\n        // Trading/Finance context\n        if (lowerText.includes(\"trading\") || lowerText.includes(\"stocks\") || lowerText.includes(\"crypto\")) {\n            return \" - risk management is everything. Never trade with money you can't afford to lose.\";\n        }\n        // AI/Tech context\n        if (lowerText.includes(\"ai\") || lowerText.includes(\"artificial intelligence\") || lowerText.includes(\"machine learning\")) {\n            return \" is transforming how we work. The key is learning to collaborate with AI, not compete against it.\";\n        }\n        // Productivity context\n        if (lowerText.includes(\"productivity\") || lowerText.includes(\"workflow\") || lowerText.includes(\"efficiency\")) {\n            return \": 1) Single-task focus 2) Time blocking 3) Automate repetitive work. Small changes, big results.\";\n        }\n        // Building/Entrepreneurship context\n        if (lowerText.includes(\"building\") || lowerText.includes(\"startup\") || lowerText.includes(\"business\")) {\n            return \" in public. Share your journey, failures, and wins. Your audience wants authenticity, not perfection.\";\n        }\n        // Learning/Growth context\n        if (lowerText.includes(\"learning\") || lowerText.includes(\"skill\") || lowerText.includes(\"growth\")) {\n            return \" - the best investment you can make is in yourself. Consistency beats intensity every time.\";\n        }\n        // Default contextual suggestions\n        const endings = [\n            \" - here's what I learned from 5 years of experience.\",\n            \". The biggest mistake I see people make is...\",\n            \". Here are 3 things that changed everything for me:\",\n            \" - and it completely shifted my perspective.\",\n            \". If I started over today, I'd focus on this first.\"\n        ];\n        return endings[Math.floor(Math.random() * endings.length)];\n    };\n    // AI prediction with context awareness\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (content.length > 15 && aiEnabled) {\n            const timer = setTimeout(()=>{\n                const suggestion = generateContextualSuggestion(content);\n                setAiSuggestion(suggestion);\n                setShowSuggestion(true);\n            }, 800);\n            return ()=>clearTimeout(timer);\n        } else {\n            setShowSuggestion(false);\n        }\n    }, [\n        content,\n        aiEnabled\n    ]);\n    const handleTabPress = (e)=>{\n        if (e.key === \"Tab\" && showSuggestion) {\n            e.preventDefault();\n            setContent(content + aiSuggestion);\n            setShowSuggestion(false);\n            setAiSuggestion(\"\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"32px\",\n            height: \"100vh\",\n            overflow: \"auto\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    marginBottom: \"32px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        style: {\n                            color: colors.text.primary,\n                            margin: 0,\n                            fontSize: \"28px\",\n                            fontWeight: \"600\",\n                            letterSpacing: \"-0.5px\",\n                            marginBottom: \"8px\"\n                        },\n                        children: \"Drafting Desk\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        style: {\n                            color: colors.text.secondary,\n                            fontSize: \"16px\",\n                            margin: 0,\n                            fontWeight: \"400\",\n                            marginBottom: \"20px\"\n                        },\n                        children: \"Your intelligent writing companion\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"flex\",\n                            gap: \"4px\",\n                            padding: \"4px\",\n                            background: colors.surface,\n                            borderRadius: \"8px\",\n                            border: \"1px solid \".concat(colors.border),\n                            width: \"fit-content\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setMode(\"manual\"),\n                                style: {\n                                    padding: \"8px 16px\",\n                                    borderRadius: \"6px\",\n                                    border: \"none\",\n                                    background: mode === \"manual\" ? colors.primary : \"transparent\",\n                                    color: mode === \"manual\" ? \"white\" : colors.text.secondary,\n                                    fontSize: \"14px\",\n                                    fontWeight: \"500\",\n                                    cursor: \"pointer\",\n                                    transition: \"all 0.2s ease\"\n                                },\n                                children: \"Manual\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setMode(\"ai\"),\n                                style: {\n                                    padding: \"8px 16px\",\n                                    borderRadius: \"6px\",\n                                    border: \"none\",\n                                    background: mode === \"ai\" ? colors.primary : \"transparent\",\n                                    color: mode === \"ai\" ? \"white\" : colors.text.secondary,\n                                    fontSize: \"14px\",\n                                    fontWeight: \"500\",\n                                    cursor: \"pointer\",\n                                    transition: \"all 0.2s ease\"\n                                },\n                                children: \"AI Assist\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: \"grid\",\n                    gridTemplateColumns: \"240px 1fr 240px\",\n                    gap: \"20px\",\n                    height: \"calc(100vh - 180px)\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: colors.surface,\n                            borderRadius: \"12px\",\n                            padding: \"20px\",\n                            boxShadow: \"0 2px 8px rgba(0, 0, 0, 0.04)\",\n                            border: \"1px solid \".concat(colors.border),\n                            overflow: \"auto\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    color: colors.text.primary,\n                                    margin: 0,\n                                    fontSize: \"16px\",\n                                    fontWeight: \"600\",\n                                    marginBottom: \"16px\"\n                                },\n                                children: \"Ideas & Hooks\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: \"20px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        style: {\n                                            color: colors.text.tertiary,\n                                            fontSize: \"12px\",\n                                            fontWeight: \"600\",\n                                            marginBottom: \"8px\",\n                                            textTransform: \"uppercase\",\n                                            letterSpacing: \"0.5px\"\n                                        },\n                                        children: \"Saved Ideas\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    [\n                                        \"AI productivity workflows\",\n                                        \"Building in public lessons\",\n                                        \"Remote work tips\",\n                                        \"Content creation process\"\n                                    ].map((idea, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                padding: \"8px 12px\",\n                                                background: \"\".concat(colors.primary, \"05\"),\n                                                borderRadius: \"6px\",\n                                                marginBottom: \"6px\",\n                                                cursor: \"pointer\",\n                                                transition: \"all 0.2s ease\",\n                                                border: \"1px solid \".concat(colors.primary, \"10\")\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    color: colors.text.secondary,\n                                                    fontSize: \"13px\"\n                                                },\n                                                children: idea\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, index, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 15\n                                        }, undefined))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        style: {\n                                            color: colors.text.tertiary,\n                                            fontSize: \"12px\",\n                                            fontWeight: \"600\",\n                                            marginBottom: \"8px\",\n                                            textTransform: \"uppercase\",\n                                            letterSpacing: \"0.5px\"\n                                        },\n                                        children: \"Trending Hooks\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    [\n                                        \"Here's what I learned...\",\n                                        \"The biggest mistake I see...\",\n                                        \"3 things that changed my...\",\n                                        \"If I started over today...\"\n                                    ].map((hook, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                padding: \"8px 12px\",\n                                                background: \"\".concat(colors.primary, \"08\"),\n                                                borderRadius: \"6px\",\n                                                marginBottom: \"6px\",\n                                                cursor: \"pointer\",\n                                                transition: \"all 0.2s ease\",\n                                                border: \"1px solid \".concat(colors.primary, \"15\")\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    color: colors.text.secondary,\n                                                    fontSize: \"13px\",\n                                                    fontStyle: \"italic\"\n                                                },\n                                                children: [\n                                                    '\"',\n                                                    hook,\n                                                    '\"'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, index, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 15\n                                        }, undefined))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: colors.surface,\n                            borderRadius: \"12px\",\n                            padding: \"24px\",\n                            boxShadow: \"0 4px 16px rgba(0, 0, 0, 0.04)\",\n                            border: \"1px solid \".concat(colors.border),\n                            position: \"relative\",\n                            display: \"flex\",\n                            flexDirection: \"column\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: \"16px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        style: {\n                                            color: colors.text.primary,\n                                            margin: 0,\n                                            fontSize: \"18px\",\n                                            fontWeight: \"600\",\n                                            marginBottom: \"4px\"\n                                        },\n                                        children: mode === \"ai\" ? \"AI-Powered Writing\" : \"Manual Writing\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    mode === \"ai\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        style: {\n                                            color: colors.text.tertiary,\n                                            fontSize: \"13px\",\n                                            margin: 0,\n                                            fontWeight: \"400\"\n                                        },\n                                        children: \"Start typing and press Tab to accept AI suggestions\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 284,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 273,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    flex: 1,\n                                    position: \"relative\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        ref: textareaRef,\n                                        value: content,\n                                        onChange: (e)=>setContent(e.target.value),\n                                        onKeyDown: handleTabPress,\n                                        placeholder: mode === \"ai\" ? \"Start writing and I'll help you continue...\" : \"What's on your mind?\",\n                                        style: {\n                                            width: \"100%\",\n                                            height: \"100%\",\n                                            minHeight: \"320px\",\n                                            padding: \"20px\",\n                                            borderRadius: \"8px\",\n                                            background: \"#FEFEFE\",\n                                            fontSize: \"16px\",\n                                            lineHeight: \"1.6\",\n                                            color: colors.text.primary,\n                                            fontFamily: '-apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif',\n                                            resize: \"none\",\n                                            outline: \"none\",\n                                            boxShadow: \"inset 0 1px 3px rgba(0, 0, 0, 0.06)\",\n                                            border: \"1px solid \".concat(colors.border),\n                                            transition: \"border-color 0.2s ease\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 297,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    showSuggestion && mode === \"ai\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            position: \"absolute\",\n                                            bottom: \"24px\",\n                                            left: \"24px\",\n                                            right: \"24px\",\n                                            padding: \"12px 16px\",\n                                            background: colors.primary,\n                                            borderRadius: \"8px\",\n                                            color: \"white\",\n                                            fontSize: \"14px\",\n                                            boxShadow: \"0 4px 12px \".concat(colors.primary, \"40\"),\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            gap: \"8px\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    flex: 1\n                                                },\n                                                children: aiSuggestion\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                lineNumber: 339,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    padding: \"2px 6px\",\n                                                    background: \"rgba(255, 255, 255, 0.2)\",\n                                                    borderRadius: \"4px\",\n                                                    fontSize: \"11px\",\n                                                    fontWeight: \"600\"\n                                                },\n                                                children: \"Tab\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                lineNumber: 342,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 296,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    justifyContent: \"space-between\",\n                                    alignItems: \"center\",\n                                    marginTop: \"16px\",\n                                    paddingTop: \"16px\",\n                                    borderTop: \"1px solid \".concat(colors.border)\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            color: colors.text.tertiary,\n                                            fontSize: \"13px\",\n                                            fontWeight: \"500\"\n                                        },\n                                        children: [\n                                            content.length,\n                                            \"/280 characters\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 364,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            gap: \"8px\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                style: {\n                                                    padding: \"8px 16px\",\n                                                    background: colors.surface,\n                                                    color: colors.text.primary,\n                                                    border: \"1px solid \".concat(colors.border),\n                                                    borderRadius: \"6px\",\n                                                    fontSize: \"13px\",\n                                                    fontWeight: \"500\",\n                                                    cursor: \"pointer\",\n                                                    transition: \"all 0.2s ease\"\n                                                },\n                                                children: \"Save Draft\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                lineNumber: 373,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                style: {\n                                                    padding: \"8px 16px\",\n                                                    background: colors.primary,\n                                                    color: \"white\",\n                                                    border: \"none\",\n                                                    borderRadius: \"6px\",\n                                                    fontSize: \"13px\",\n                                                    fontWeight: \"600\",\n                                                    cursor: \"pointer\",\n                                                    transition: \"all 0.2s ease\"\n                                                },\n                                                children: \"Post Tweet\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                lineNumber: 387,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 372,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 356,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 263,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: colors.surface,\n                            borderRadius: \"12px\",\n                            padding: \"20px\",\n                            boxShadow: \"0 2px 8px rgba(0, 0, 0, 0.04)\",\n                            border: \"1px solid \".concat(colors.border),\n                            overflow: \"auto\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    color: colors.text.primary,\n                                    margin: 0,\n                                    fontSize: \"16px\",\n                                    fontWeight: \"600\",\n                                    marginBottom: \"16px\"\n                                },\n                                children: \"AI Mentor\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 413,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    background: \"\".concat(colors.primary, \"08\"),\n                                    borderRadius: \"8px\",\n                                    padding: \"16px\",\n                                    marginBottom: \"16px\",\n                                    border: \"1px solid \".concat(colors.primary, \"15\")\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            color: colors.text.primary,\n                                            fontSize: \"13px\",\n                                            fontWeight: \"600\",\n                                            marginBottom: \"8px\"\n                                        },\n                                        children: \"Real-time Tip\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 431,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        style: {\n                                            color: colors.text.secondary,\n                                            fontSize: \"13px\",\n                                            margin: 0,\n                                            lineHeight: \"1.4\"\n                                        },\n                                        children: \"Try starting with a question or bold statement. Your audience loves content that makes them think or challenges their assumptions.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 439,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 424,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: \"16px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        style: {\n                                            color: colors.text.tertiary,\n                                            fontSize: \"12px\",\n                                            fontWeight: \"600\",\n                                            marginBottom: \"8px\",\n                                            textTransform: \"uppercase\",\n                                            letterSpacing: \"0.5px\"\n                                        },\n                                        children: \"Your Brand Tone\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 451,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    [\n                                        {\n                                            tone: \"Helpful\",\n                                            desc: \"Share actionable insights\"\n                                        },\n                                        {\n                                            tone: \"Authentic\",\n                                            desc: \"Be genuine and personal\"\n                                        },\n                                        {\n                                            tone: \"Inspiring\",\n                                            desc: \"Motivate and encourage\"\n                                        }\n                                    ].map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                padding: \"8px 12px\",\n                                                background: \"\".concat(colors.primary, \"05\"),\n                                                borderRadius: \"6px\",\n                                                marginBottom: \"6px\",\n                                                border: \"1px solid \".concat(colors.primary, \"10\")\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        color: colors.text.primary,\n                                                        fontSize: \"13px\",\n                                                        fontWeight: \"600\",\n                                                        marginBottom: \"2px\"\n                                                    },\n                                                    children: item.tone\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                    lineNumber: 473,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        color: colors.text.tertiary,\n                                                        fontSize: \"11px\"\n                                                    },\n                                                    children: item.desc\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                    lineNumber: 481,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                            lineNumber: 466,\n                                            columnNumber: 15\n                                        }, undefined))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 450,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        style: {\n                                            color: colors.text.tertiary,\n                                            fontSize: \"12px\",\n                                            fontWeight: \"600\",\n                                            marginBottom: \"8px\",\n                                            textTransform: \"uppercase\",\n                                            letterSpacing: \"0.5px\"\n                                        },\n                                        children: \"What's Working\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 493,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    [\n                                        'Posts with \"AI\" get 3x engagement',\n                                        \"Questions drive 2x more replies\",\n                                        \"Behind-the-scenes content performs well\"\n                                    ].map((insight, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                padding: \"8px 12px\",\n                                                background: \"#F0F9FF\",\n                                                borderRadius: \"6px\",\n                                                marginBottom: \"6px\",\n                                                border: \"1px solid #E0F2FE\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    color: colors.text.secondary,\n                                                    fontSize: \"12px\"\n                                                },\n                                                children: insight\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                lineNumber: 515,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, index, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                            lineNumber: 508,\n                                            columnNumber: 15\n                                        }, undefined))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 492,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 405,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                lineNumber: 163,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n        lineNumber: 95,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TweetCenterPage, \"W1iqltz8Ex7S1lPVCdZjX12Wqp0=\");\n_c = TweetCenterPage;\nTweetCenterPage.getLayout = function getLayout(page) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SidebarLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: page\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n        lineNumber: 532,\n        columnNumber: 5\n    }, this);\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (TweetCenterPage);\nvar _c;\n$RefreshReg$(_c, \"TweetCenterPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/tweet-center.tsx\n"));

/***/ })

});