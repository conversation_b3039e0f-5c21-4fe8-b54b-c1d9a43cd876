"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./pages/index.tsx":
/*!*************************!*\
  !*** ./pages/index.tsx ***!
  \*************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_SidebarLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/SidebarLayout */ \"./components/SidebarLayout.tsx\");\n/* harmony import */ var _barrel_optimize_names_MessageCircle_Sparkles_Target_TrendingUp_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=MessageCircle,Sparkles,Target,TrendingUp,Users,Video!=!lucide-react */ \"__barrel_optimize__?names=MessageCircle,Sparkles,Target,TrendingUp,Users,Video!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n// pages/index.tsx\n\n\n\n\n\nconst HomePage = ()=>{\n    const colors = {\n        primary: \"#FF6B35\",\n        primaryLight: \"#FF8A65\",\n        text: {\n            primary: \"#2D1B14\",\n            secondary: \"#5D4037\",\n            tertiary: \"#8D6E63\"\n        },\n        border: \"#F5E6D3\",\n        surface: \"#FFFFFF\",\n        warmGlow: \"#FFE0B2\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"60px 80px\",\n            height: \"100vh\",\n            overflow: \"auto\",\n            background: \"transparent\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    marginBottom: \"50px\",\n                    textAlign: \"center\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"cursive-header\",\n                        style: {\n                            color: colors.text.primary,\n                            margin: 0,\n                            fontSize: \"48px\",\n                            fontWeight: \"400\",\n                            letterSpacing: \"-1px\",\n                            marginBottom: \"16px\",\n                            textShadow: \"0 2px 4px rgba(0, 0, 0, 0.1)\"\n                        },\n                        children: \"Briefing Room\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"sf-pro\",\n                        style: {\n                            color: colors.text.secondary,\n                            fontSize: \"18px\",\n                            margin: 0,\n                            fontWeight: \"400\",\n                            opacity: 0.8\n                        },\n                        children: \"Your daily mission control center\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    maxWidth: \"1000px\",\n                    margin: \"0 auto\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: colors.surface,\n                            borderRadius: \"20px\",\n                            padding: \"40px\",\n                            marginBottom: \"30px\",\n                            boxShadow: \"\\n            0 8px 32px rgba(0, 0, 0, 0.06),\\n            0 2px 8px rgba(0, 0, 0, 0.04),\\n            inset 0 1px 0 rgba(255, 255, 255, 0.9)\\n          \",\n                            border: \"1px solid \".concat(colors.border),\n                            position: \"relative\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    position: \"absolute\",\n                                    top: 0,\n                                    left: 0,\n                                    right: 0,\n                                    height: \"100px\",\n                                    background: \"radial-gradient(ellipse at top, \".concat(colors.warmGlow, \"15 0%, transparent 70%)\"),\n                                    pointerEvents: \"none\",\n                                    borderRadius: \"20px 20px 0 0\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    position: \"relative\",\n                                    zIndex: 1\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            justifyContent: \"space-between\",\n                                            marginBottom: \"20px\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: \"flex\",\n                                                    alignItems: \"center\",\n                                                    gap: \"12px\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            width: \"40px\",\n                                                            height: \"40px\",\n                                                            borderRadius: \"10px\",\n                                                            background: \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\"),\n                                                            display: \"flex\",\n                                                            alignItems: \"center\",\n                                                            justifyContent: \"center\",\n                                                            boxShadow: \"0 4px 12px \".concat(colors.primary, \"30\")\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_Sparkles_Target_TrendingUp_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Target, {\n                                                            size: 20,\n                                                            color: \"white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                            lineNumber: 101,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                        lineNumber: 91,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"elegant-cursive\",\n                                                        style: {\n                                                            color: colors.text.primary,\n                                                            margin: 0,\n                                                            fontSize: \"28px\",\n                                                            fontWeight: \"400\"\n                                                        },\n                                                        children: \"Today's Mission\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                        lineNumber: 103,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                lineNumber: 90,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: \"flex\",\n                                                    alignItems: \"center\",\n                                                    gap: \"6px\",\n                                                    padding: \"6px 12px\",\n                                                    background: \"\".concat(colors.primary, \"15\"),\n                                                    borderRadius: \"20px\",\n                                                    border: \"1px solid \".concat(colors.primary, \"25\")\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_Sparkles_Target_TrendingUp_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Sparkles, {\n                                                        size: 12,\n                                                        color: colors.primary\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                        lineNumber: 121,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"sf-pro\",\n                                                        style: {\n                                                            color: colors.primary,\n                                                            fontSize: \"11px\",\n                                                            fontWeight: \"600\",\n                                                            textTransform: \"uppercase\",\n                                                            letterSpacing: \"0.5px\"\n                                                        },\n                                                        children: \"AI Generated\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                        lineNumber: 122,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"sf-pro\",\n                                        style: {\n                                            color: colors.text.secondary,\n                                            fontSize: \"17px\",\n                                            lineHeight: \"1.6\",\n                                            margin: 0,\n                                            marginBottom: \"30px\"\n                                        },\n                                        children: \"Focus on engaging with your audience about AI productivity tools. Your recent posts about automation got 3x more engagement. Consider sharing a behind-the-scenes story about how AI helps your workflow.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"grid\",\n                                            gridTemplateColumns: \"repeat(3, 1fr)\",\n                                            gap: \"20px\",\n                                            marginBottom: \"30px\"\n                                        },\n                                        children: [\n                                            {\n                                                label: \"Engagement Rate\",\n                                                value: \"+24%\",\n                                                icon: _barrel_optimize_names_MessageCircle_Sparkles_Target_TrendingUp_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.TrendingUp,\n                                                color: \"#10B981\"\n                                            },\n                                            {\n                                                label: \"New Followers\",\n                                                value: \"127\",\n                                                icon: _barrel_optimize_names_MessageCircle_Sparkles_Target_TrendingUp_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Users,\n                                                color: \"#3B82F6\"\n                                            },\n                                            {\n                                                label: \"Content Score\",\n                                                value: \"8.9/10\",\n                                                icon: _barrel_optimize_names_MessageCircle_Sparkles_Target_TrendingUp_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Target,\n                                                color: colors.primary\n                                            }\n                                        ].map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    background: colors.surface,\n                                                    borderRadius: \"16px\",\n                                                    padding: \"24px\",\n                                                    textAlign: \"center\",\n                                                    border: \"1px solid \".concat(colors.border),\n                                                    boxShadow: \"0 4px 12px rgba(0, 0, 0, 0.04)\",\n                                                    position: \"relative\",\n                                                    overflow: \"hidden\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            position: \"absolute\",\n                                                            top: \"-10px\",\n                                                            right: \"-10px\",\n                                                            width: \"40px\",\n                                                            height: \"40px\",\n                                                            borderRadius: \"50%\",\n                                                            background: \"\".concat(stat.color, \"15\"),\n                                                            display: \"flex\",\n                                                            alignItems: \"center\",\n                                                            justifyContent: \"center\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(stat.icon, {\n                                                            size: 16,\n                                                            color: stat.color,\n                                                            style: {\n                                                                opacity: 0.6\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                            lineNumber: 179,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                        lineNumber: 167,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"sf-pro\",\n                                                        style: {\n                                                            fontSize: \"28px\",\n                                                            fontWeight: \"700\",\n                                                            color: colors.text.primary,\n                                                            marginBottom: \"8px\"\n                                                        },\n                                                        children: stat.value\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                        lineNumber: 182,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"sf-pro\",\n                                                        style: {\n                                                            fontSize: \"14px\",\n                                                            color: colors.text.tertiary,\n                                                            fontWeight: \"500\"\n                                                        },\n                                                        children: stat.label\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                        lineNumber: 190,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                lineNumber: 156,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            gap: \"16px\",\n                                            flexWrap: \"wrap\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/meeting\",\n                                                style: {\n                                                    textDecoration: \"none\"\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"sf-pro\",\n                                                    style: {\n                                                        display: \"flex\",\n                                                        alignItems: \"center\",\n                                                        gap: \"8px\",\n                                                        padding: \"14px 24px\",\n                                                        background: \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\"),\n                                                        color: \"white\",\n                                                        border: \"none\",\n                                                        borderRadius: \"12px\",\n                                                        fontSize: \"14px\",\n                                                        fontWeight: \"600\",\n                                                        cursor: \"pointer\",\n                                                        transition: \"all 0.2s ease\",\n                                                        boxShadow: \"0 4px 16px \".concat(colors.primary, \"30\")\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_Sparkles_Target_TrendingUp_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Video, {\n                                                            size: 16\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                            lineNumber: 223,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        \"Join Call\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                    lineNumber: 208,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"sf-pro\",\n                                                style: {\n                                                    display: \"flex\",\n                                                    alignItems: \"center\",\n                                                    gap: \"8px\",\n                                                    padding: \"14px 24px\",\n                                                    background: colors.surface,\n                                                    color: colors.text.primary,\n                                                    border: \"1px solid \".concat(colors.border),\n                                                    borderRadius: \"12px\",\n                                                    fontSize: \"14px\",\n                                                    fontWeight: \"600\",\n                                                    cursor: \"pointer\",\n                                                    transition: \"all 0.2s ease\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_Sparkles_Target_TrendingUp_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Sparkles, {\n                                                        size: 16\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                        lineNumber: 242,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \"Ask Mentor\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/tweet-center\",\n                                                style: {\n                                                    textDecoration: \"none\"\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"sf-pro\",\n                                                    style: {\n                                                        display: \"flex\",\n                                                        alignItems: \"center\",\n                                                        gap: \"8px\",\n                                                        padding: \"14px 24px\",\n                                                        background: colors.surface,\n                                                        color: colors.text.primary,\n                                                        border: \"1px solid \".concat(colors.border),\n                                                        borderRadius: \"12px\",\n                                                        fontSize: \"14px\",\n                                                        fontWeight: \"600\",\n                                                        cursor: \"pointer\",\n                                                        transition: \"all 0.2s ease\"\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_Sparkles_Target_TrendingUp_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.MessageCircle, {\n                                                            size: 16\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                            lineNumber: 261,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        \"Create Content\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                    lineNumber: 247,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: colors.surface,\n                            borderRadius: \"20px\",\n                            padding: \"32px\",\n                            boxShadow: \"\\n            0 8px 32px rgba(0, 0, 0, 0.06),\\n            0 2px 8px rgba(0, 0, 0, 0.04),\\n            inset 0 1px 0 rgba(255, 255, 255, 0.9)\\n          \",\n                            border: \"1px solid \".concat(colors.border),\n                            position: \"relative\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    position: \"absolute\",\n                                    top: 0,\n                                    left: 0,\n                                    right: 0,\n                                    height: \"80px\",\n                                    background: \"radial-gradient(ellipse at top, \".concat(colors.warmGlow, \"12 0%, transparent 70%)\"),\n                                    pointerEvents: \"none\",\n                                    borderRadius: \"20px 20px 0 0\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                lineNumber: 283,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"flex-start\",\n                                    gap: \"20px\",\n                                    position: \"relative\",\n                                    zIndex: 1\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            width: \"56px\",\n                                            height: \"56px\",\n                                            borderRadius: \"16px\",\n                                            background: \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\"),\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            justifyContent: \"center\",\n                                            position: \"relative\",\n                                            flexShrink: 0,\n                                            boxShadow: \"0 8px 24px \".concat(colors.primary, \"30\")\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_Sparkles_Target_TrendingUp_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Sparkles, {\n                                                size: 24,\n                                                color: \"white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    position: \"absolute\",\n                                                    bottom: \"-2px\",\n                                                    right: \"-2px\",\n                                                    width: \"16px\",\n                                                    height: \"16px\",\n                                                    borderRadius: \"50%\",\n                                                    background: \"linear-gradient(135deg, #00E676 0%, #00C853 100%)\",\n                                                    border: \"3px solid white\",\n                                                    boxShadow: \"0 0 8px rgba(0, 230, 118, 0.4)\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                lineNumber: 308,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                        lineNumber: 295,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            flex: 1\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"elegant-cursive\",\n                                                style: {\n                                                    color: colors.text.primary,\n                                                    margin: 0,\n                                                    fontSize: \"24px\",\n                                                    fontWeight: \"400\",\n                                                    marginBottom: \"12px\"\n                                                },\n                                                children: \"AI Mentor\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                lineNumber: 321,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"sf-pro\",\n                                                style: {\n                                                    color: colors.text.secondary,\n                                                    margin: 0,\n                                                    fontSize: \"16px\",\n                                                    lineHeight: \"1.6\"\n                                                },\n                                                children: \"Ready to help you create content that resonates. What's on your mind today?\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                        lineNumber: 270,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, undefined);\n};\n_c = HomePage;\nHomePage.getLayout = function getLayout(page) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SidebarLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: page\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n        lineNumber: 348,\n        columnNumber: 5\n    }, this);\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (HomePage);\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/index.tsx\n"));

/***/ })

});