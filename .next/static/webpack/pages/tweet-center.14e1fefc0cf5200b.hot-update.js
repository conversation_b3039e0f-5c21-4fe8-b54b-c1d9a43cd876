"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/tweet-center",{

/***/ "./pages/tweet-center.tsx":
/*!********************************!*\
  !*** ./pages/tweet-center.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_SidebarLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/SidebarLayout */ \"./components/SidebarLayout.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlignCenter_AlignLeft_Sparkles_Type_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlignCenter,AlignLeft,Sparkles,Type!=!lucide-react */ \"__barrel_optimize__?names=AlignCenter,AlignLeft,Sparkles,Type!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n// pages/tweet-center.tsx\n\nvar _s = $RefreshSig$();\n\n\n\nconst TweetCenterPage = ()=>{\n    _s();\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [aiSuggestion, setAiSuggestion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showSuggestion, setShowSuggestion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [aiEnabled, setAiEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [formatMode, setFormatMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"single\");\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const colors = {\n        primary: \"#FF6B35\",\n        primaryLight: \"#FF8A65\",\n        text: {\n            primary: \"#2D1B14\",\n            secondary: \"#5D4037\",\n            tertiary: \"#8D6E63\"\n        },\n        border: \"#F5E6D3\",\n        surface: \"#FFFFFF\",\n        warmGlow: \"#FFE0B2\",\n        paper: \"#FEFEFE\"\n    };\n    // Intelligent AI prediction based on content context\n    const generateContextualSuggestion = (text)=>{\n        const lowerText = text.toLowerCase();\n        // Trading/Finance context\n        if (lowerText.includes(\"trading\") || lowerText.includes(\"stocks\") || lowerText.includes(\"crypto\")) {\n            return \" - risk management is everything. Never trade with money you can't afford to lose.\";\n        }\n        // AI/Tech context\n        if (lowerText.includes(\"ai\") || lowerText.includes(\"artificial intelligence\") || lowerText.includes(\"machine learning\")) {\n            return \" is transforming how we work. The key is learning to collaborate with AI, not compete against it.\";\n        }\n        // Productivity context\n        if (lowerText.includes(\"productivity\") || lowerText.includes(\"workflow\") || lowerText.includes(\"efficiency\")) {\n            return \": 1) Single-task focus 2) Time blocking 3) Automate repetitive work. Small changes, big results.\";\n        }\n        // Building/Entrepreneurship context\n        if (lowerText.includes(\"building\") || lowerText.includes(\"startup\") || lowerText.includes(\"business\")) {\n            return \" in public. Share your journey, failures, and wins. Your audience wants authenticity, not perfection.\";\n        }\n        // Learning/Growth context\n        if (lowerText.includes(\"learning\") || lowerText.includes(\"skill\") || lowerText.includes(\"growth\")) {\n            return \" - the best investment you can make is in yourself. Consistency beats intensity every time.\";\n        }\n        // Default contextual suggestions\n        const endings = [\n            \" - here's what I learned from 5 years of experience.\",\n            \". The biggest mistake I see people make is...\",\n            \". Here are 3 things that changed everything for me:\",\n            \" - and it completely shifted my perspective.\",\n            \". If I started over today, I'd focus on this first.\"\n        ];\n        return endings[Math.floor(Math.random() * endings.length)];\n    };\n    // AI prediction with context awareness\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (content.length > 15 && aiEnabled) {\n            const timer = setTimeout(()=>{\n                const suggestion = generateContextualSuggestion(content);\n                setAiSuggestion(suggestion);\n                setShowSuggestion(true);\n            }, 800);\n            return ()=>clearTimeout(timer);\n        } else {\n            setShowSuggestion(false);\n        }\n    }, [\n        content,\n        aiEnabled\n    ]);\n    const handleTabPress = (e)=>{\n        if (e.key === \"Tab\" && showSuggestion) {\n            e.preventDefault();\n            setContent(content + aiSuggestion);\n            setShowSuggestion(false);\n            setAiSuggestion(\"\");\n        }\n    };\n    const autoFormat = ()=>{\n        if (content.length > 280) {\n            // Convert to thread format\n            const sentences = content.split(\". \");\n            const threads = [];\n            let currentThread = \"\";\n            sentences.forEach((sentence)=>{\n                if ((currentThread + sentence + \". \").length <= 280) {\n                    currentThread += sentence + \". \";\n                } else {\n                    if (currentThread) threads.push(currentThread.trim());\n                    currentThread = sentence + \". \";\n                }\n            });\n            if (currentThread) threads.push(currentThread.trim());\n            setContent(threads.join(\"\\n\\n\"));\n            setFormatMode(\"thread\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"40px 60px\",\n            height: \"100vh\",\n            overflow: \"auto\",\n            background: colors.paper,\n            display: \"flex\",\n            flexDirection: \"column\",\n            alignItems: \"center\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    width: \"100%\",\n                    maxWidth: \"800px\",\n                    marginBottom: \"40px\",\n                    textAlign: \"center\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        style: {\n                            color: colors.text.primary,\n                            margin: 0,\n                            fontSize: \"32px\",\n                            fontWeight: \"300\",\n                            letterSpacing: \"-1px\",\n                            marginBottom: \"12px\",\n                            fontFamily: \"Georgia, serif\"\n                        },\n                        children: \"Drafting Desk\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"flex\",\n                            justifyContent: \"center\",\n                            alignItems: \"center\",\n                            gap: \"16px\",\n                            marginTop: \"24px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"8px\",\n                                    padding: \"6px 12px\",\n                                    background: aiEnabled ? \"\".concat(colors.primary, \"15\") : colors.surface,\n                                    borderRadius: \"20px\",\n                                    border: \"1px solid \".concat(aiEnabled ? colors.primary : colors.border),\n                                    cursor: \"pointer\",\n                                    transition: \"all 0.2s ease\"\n                                },\n                                onClick: ()=>setAiEnabled(!aiEnabled),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_Sparkles_Type_lucide_react__WEBPACK_IMPORTED_MODULE_3__.Sparkles, {\n                                        size: 14,\n                                        color: aiEnabled ? colors.primary : colors.text.tertiary\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        style: {\n                                            fontSize: \"13px\",\n                                            fontWeight: \"500\",\n                                            color: aiEnabled ? colors.primary : colors.text.tertiary\n                                        },\n                                        children: \"AI Assistant\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: autoFormat,\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"6px\",\n                                    padding: \"6px 12px\",\n                                    background: colors.surface,\n                                    border: \"1px solid \".concat(colors.border),\n                                    borderRadius: \"20px\",\n                                    fontSize: \"13px\",\n                                    fontWeight: \"500\",\n                                    color: colors.text.secondary,\n                                    cursor: \"pointer\",\n                                    transition: \"all 0.2s ease\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_Sparkles_Type_lucide_react__WEBPACK_IMPORTED_MODULE_3__.Type, {\n                                        size: 14\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Auto Format\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"6px\",\n                                    padding: \"6px 12px\",\n                                    background: formatMode === \"thread\" ? \"\".concat(colors.primary, \"15\") : colors.surface,\n                                    borderRadius: \"20px\",\n                                    border: \"1px solid \".concat(formatMode === \"thread\" ? colors.primary : colors.border)\n                                },\n                                children: [\n                                    formatMode === \"thread\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_Sparkles_Type_lucide_react__WEBPACK_IMPORTED_MODULE_3__.AlignLeft, {\n                                        size: 14,\n                                        color: colors.primary\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 40\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_Sparkles_Type_lucide_react__WEBPACK_IMPORTED_MODULE_3__.AlignCenter, {\n                                        size: 14,\n                                        color: colors.text.tertiary\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 89\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        style: {\n                                            fontSize: \"13px\",\n                                            fontWeight: \"500\",\n                                            color: formatMode === \"thread\" ? colors.primary : colors.text.tertiary\n                                        },\n                                        children: formatMode === \"thread\" ? \"Thread\" : \"Single\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                lineNumber: 127,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    width: \"100%\",\n                    maxWidth: \"800px\",\n                    background: colors.surface,\n                    borderRadius: \"16px\",\n                    padding: \"60px 80px\",\n                    boxShadow: \"\\n          0 8px 32px rgba(0, 0, 0, 0.06),\\n          0 2px 8px rgba(0, 0, 0, 0.04),\\n          inset 0 1px 0 rgba(255, 255, 255, 0.9)\\n        \",\n                    border: \"1px solid \".concat(colors.border),\n                    position: \"relative\",\n                    minHeight: \"500px\",\n                    display: \"flex\",\n                    flexDirection: \"column\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            position: \"absolute\",\n                            top: 0,\n                            left: 0,\n                            right: 0,\n                            bottom: 0,\n                            background: \"\\n            repeating-linear-gradient(\\n              0deg,\\n              transparent,\\n              transparent 24px,\\n              rgba(0, 0, 0, 0.02) 25px\\n            )\\n          \",\n                            pointerEvents: \"none\",\n                            borderRadius: \"16px\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 240,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: colors.surface,\n                            borderRadius: \"12px\",\n                            padding: \"24px\",\n                            boxShadow: \"0 4px 16px rgba(0, 0, 0, 0.04)\",\n                            border: \"1px solid \".concat(colors.border),\n                            position: \"relative\",\n                            display: \"flex\",\n                            flexDirection: \"column\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: \"16px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        style: {\n                                            color: colors.text.primary,\n                                            margin: 0,\n                                            fontSize: \"18px\",\n                                            fontWeight: \"600\",\n                                            marginBottom: \"4px\"\n                                        },\n                                        children: mode === \"ai\" ? \"AI-Powered Writing\" : \"Manual Writing\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    mode === \"ai\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        style: {\n                                            color: colors.text.tertiary,\n                                            fontSize: \"13px\",\n                                            margin: 0,\n                                            fontWeight: \"400\"\n                                        },\n                                        children: \"Start typing and press Tab to accept AI suggestions\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    flex: 1,\n                                    position: \"relative\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        ref: textareaRef,\n                                        value: content,\n                                        onChange: (e)=>setContent(e.target.value),\n                                        onKeyDown: handleTabPress,\n                                        placeholder: mode === \"ai\" ? \"Start writing and I'll help you continue...\" : \"What's on your mind?\",\n                                        style: {\n                                            width: \"100%\",\n                                            height: \"100%\",\n                                            minHeight: \"320px\",\n                                            padding: \"20px\",\n                                            borderRadius: \"8px\",\n                                            background: \"#FEFEFE\",\n                                            fontSize: \"16px\",\n                                            lineHeight: \"1.6\",\n                                            color: colors.text.primary,\n                                            fontFamily: '-apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif',\n                                            resize: \"none\",\n                                            outline: \"none\",\n                                            boxShadow: \"inset 0 1px 3px rgba(0, 0, 0, 0.06)\",\n                                            border: \"1px solid \".concat(colors.border),\n                                            transition: \"border-color 0.2s ease\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 293,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    showSuggestion && mode === \"ai\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            position: \"absolute\",\n                                            bottom: \"24px\",\n                                            left: \"24px\",\n                                            right: \"24px\",\n                                            padding: \"12px 16px\",\n                                            background: colors.primary,\n                                            borderRadius: \"8px\",\n                                            color: \"white\",\n                                            fontSize: \"14px\",\n                                            boxShadow: \"0 4px 12px \".concat(colors.primary, \"40\"),\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            gap: \"8px\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    flex: 1\n                                                },\n                                                children: aiSuggestion\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                lineNumber: 335,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    padding: \"2px 6px\",\n                                                    background: \"rgba(255, 255, 255, 0.2)\",\n                                                    borderRadius: \"4px\",\n                                                    fontSize: \"11px\",\n                                                    fontWeight: \"600\"\n                                                },\n                                                children: \"Tab\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                lineNumber: 338,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    justifyContent: \"space-between\",\n                                    alignItems: \"center\",\n                                    marginTop: \"16px\",\n                                    paddingTop: \"16px\",\n                                    borderTop: \"1px solid \".concat(colors.border)\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            color: colors.text.tertiary,\n                                            fontSize: \"13px\",\n                                            fontWeight: \"500\"\n                                        },\n                                        children: [\n                                            content.length,\n                                            \"/280 characters\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 360,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            gap: \"8px\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                style: {\n                                                    padding: \"8px 16px\",\n                                                    background: colors.surface,\n                                                    color: colors.text.primary,\n                                                    border: \"1px solid \".concat(colors.border),\n                                                    borderRadius: \"6px\",\n                                                    fontSize: \"13px\",\n                                                    fontWeight: \"500\",\n                                                    cursor: \"pointer\",\n                                                    transition: \"all 0.2s ease\"\n                                                },\n                                                children: \"Save Draft\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                lineNumber: 369,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                style: {\n                                                    padding: \"8px 16px\",\n                                                    background: colors.primary,\n                                                    color: \"white\",\n                                                    border: \"none\",\n                                                    borderRadius: \"6px\",\n                                                    fontSize: \"13px\",\n                                                    fontWeight: \"600\",\n                                                    cursor: \"pointer\",\n                                                    transition: \"all 0.2s ease\"\n                                                },\n                                                children: \"Post Tweet\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                lineNumber: 383,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 368,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 352,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 259,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: colors.surface,\n                            borderRadius: \"12px\",\n                            padding: \"20px\",\n                            boxShadow: \"0 2px 8px rgba(0, 0, 0, 0.04)\",\n                            border: \"1px solid \".concat(colors.border),\n                            overflow: \"auto\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    color: colors.text.primary,\n                                    margin: 0,\n                                    fontSize: \"16px\",\n                                    fontWeight: \"600\",\n                                    marginBottom: \"16px\"\n                                },\n                                children: \"AI Mentor\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 409,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    background: \"\".concat(colors.primary, \"08\"),\n                                    borderRadius: \"8px\",\n                                    padding: \"16px\",\n                                    marginBottom: \"16px\",\n                                    border: \"1px solid \".concat(colors.primary, \"15\")\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            color: colors.text.primary,\n                                            fontSize: \"13px\",\n                                            fontWeight: \"600\",\n                                            marginBottom: \"8px\"\n                                        },\n                                        children: \"Real-time Tip\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 427,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        style: {\n                                            color: colors.text.secondary,\n                                            fontSize: \"13px\",\n                                            margin: 0,\n                                            lineHeight: \"1.4\"\n                                        },\n                                        children: \"Try starting with a question or bold statement. Your audience loves content that makes them think or challenges their assumptions.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 435,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 420,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: \"16px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        style: {\n                                            color: colors.text.tertiary,\n                                            fontSize: \"12px\",\n                                            fontWeight: \"600\",\n                                            marginBottom: \"8px\",\n                                            textTransform: \"uppercase\",\n                                            letterSpacing: \"0.5px\"\n                                        },\n                                        children: \"Your Brand Tone\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 447,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    [\n                                        {\n                                            tone: \"Helpful\",\n                                            desc: \"Share actionable insights\"\n                                        },\n                                        {\n                                            tone: \"Authentic\",\n                                            desc: \"Be genuine and personal\"\n                                        },\n                                        {\n                                            tone: \"Inspiring\",\n                                            desc: \"Motivate and encourage\"\n                                        }\n                                    ].map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                padding: \"8px 12px\",\n                                                background: \"\".concat(colors.primary, \"05\"),\n                                                borderRadius: \"6px\",\n                                                marginBottom: \"6px\",\n                                                border: \"1px solid \".concat(colors.primary, \"10\")\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        color: colors.text.primary,\n                                                        fontSize: \"13px\",\n                                                        fontWeight: \"600\",\n                                                        marginBottom: \"2px\"\n                                                    },\n                                                    children: item.tone\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                    lineNumber: 469,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        color: colors.text.tertiary,\n                                                        fontSize: \"11px\"\n                                                    },\n                                                    children: item.desc\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                    lineNumber: 477,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                            lineNumber: 462,\n                                            columnNumber: 15\n                                        }, undefined))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 446,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        style: {\n                                            color: colors.text.tertiary,\n                                            fontSize: \"12px\",\n                                            fontWeight: \"600\",\n                                            marginBottom: \"8px\",\n                                            textTransform: \"uppercase\",\n                                            letterSpacing: \"0.5px\"\n                                        },\n                                        children: \"What's Working\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 489,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    [\n                                        'Posts with \"AI\" get 3x engagement',\n                                        \"Questions drive 2x more replies\",\n                                        \"Behind-the-scenes content performs well\"\n                                    ].map((insight, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                padding: \"8px 12px\",\n                                                background: \"#F0F9FF\",\n                                                borderRadius: \"6px\",\n                                                marginBottom: \"6px\",\n                                                border: \"1px solid #E0F2FE\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    color: colors.text.secondary,\n                                                    fontSize: \"12px\"\n                                                },\n                                                children: insight\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                lineNumber: 511,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, index, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                            lineNumber: 504,\n                                            columnNumber: 15\n                                        }, undefined))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 488,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 401,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                lineNumber: 222,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n        lineNumber: 117,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TweetCenterPage, \"W1iqltz8Ex7S1lPVCdZjX12Wqp0=\");\n_c = TweetCenterPage;\nTweetCenterPage.getLayout = function getLayout(page) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SidebarLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: page\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n        lineNumber: 528,\n        columnNumber: 5\n    }, this);\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (TweetCenterPage);\nvar _c;\n$RefreshReg$(_c, \"TweetCenterPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/tweet-center.tsx\n"));

/***/ })

});