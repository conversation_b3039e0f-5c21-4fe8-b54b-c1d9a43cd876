"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "__barrel_optimize__?names=MessageCircle,Sparkles,Target,TrendingUp,Users,Video!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!*********************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=MessageCircle,Sparkles,Target,TrendingUp,Users,Video!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \*********************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MessageCircle: function() { return /* reexport safe */ _icons_message_circle_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]; },\n/* harmony export */   Sparkles: function() { return /* reexport safe */ _icons_sparkles_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]; },\n/* harmony export */   Target: function() { return /* reexport safe */ _icons_target_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]; },\n/* harmony export */   TrendingUp: function() { return /* reexport safe */ _icons_trending_up_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]; },\n/* harmony export */   Users: function() { return /* reexport safe */ _icons_users_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]; },\n/* harmony export */   Video: function() { return /* reexport safe */ _icons_video_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]; }\n/* harmony export */ });\n/* harmony import */ var _icons_message_circle_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/message-circle.js */ \"./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _icons_sparkles_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/sparkles.js */ \"./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _icons_target_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/target.js */ \"./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _icons_trending_up_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/trending-up.js */ \"./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _icons_users_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./icons/users.js */ \"./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _icons_video_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./icons/video.js */ \"./node_modules/lucide-react/dist/esm/icons/video.js\");\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1NZXNzYWdlQ2lyY2xlLFNwYXJrbGVzLFRhcmdldCxUcmVuZGluZ1VwLFVzZXJzLFZpZGVvIT0hLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2x1Y2lkZS1yZWFjdC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBQ29FO0FBQ1g7QUFDSjtBQUNTO0FBQ1giLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9sdWNpZGUtcmVhY3QuanM/MjQ3ZCJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgTWVzc2FnZUNpcmNsZSB9IGZyb20gXCIuL2ljb25zL21lc3NhZ2UtY2lyY2xlLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgU3BhcmtsZXMgfSBmcm9tIFwiLi9pY29ucy9zcGFya2xlcy5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFRhcmdldCB9IGZyb20gXCIuL2ljb25zL3RhcmdldC5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFRyZW5kaW5nVXAgfSBmcm9tIFwiLi9pY29ucy90cmVuZGluZy11cC5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFVzZXJzIH0gZnJvbSBcIi4vaWNvbnMvdXNlcnMuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBWaWRlbyB9IGZyb20gXCIuL2ljb25zL3ZpZGVvLmpzXCIiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=MessageCircle,Sparkles,Target,TrendingUp,Users,Video!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n"));

/***/ }),

/***/ "./pages/index.tsx":
/*!*************************!*\
  !*** ./pages/index.tsx ***!
  \*************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_SidebarLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/SidebarLayout */ \"./components/SidebarLayout.tsx\");\n/* harmony import */ var _barrel_optimize_names_MessageCircle_Sparkles_Target_TrendingUp_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=MessageCircle,Sparkles,Target,TrendingUp,Users,Video!=!lucide-react */ \"__barrel_optimize__?names=MessageCircle,Sparkles,Target,TrendingUp,Users,Video!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n// pages/index.tsx\n\n\n\n\n\nconst HomePage = ()=>{\n    const colors = {\n        primary: \"#FF6B35\",\n        primaryLight: \"#FF8A65\",\n        text: {\n            primary: \"#2D1B14\",\n            secondary: \"#5D4037\",\n            tertiary: \"#8D6E63\"\n        },\n        border: \"#F5E6D3\",\n        surface: \"#FFFFFF\",\n        warmGlow: \"#FFE0B2\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"60px 80px\",\n            height: \"100vh\",\n            overflow: \"auto\",\n            background: \"transparent\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    marginBottom: \"50px\",\n                    textAlign: \"center\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"cursive-header\",\n                        style: {\n                            color: colors.text.primary,\n                            margin: 0,\n                            fontSize: \"48px\",\n                            fontWeight: \"400\",\n                            letterSpacing: \"-1px\",\n                            marginBottom: \"16px\",\n                            textShadow: \"0 2px 4px rgba(0, 0, 0, 0.1)\"\n                        },\n                        children: \"Briefing Room\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"sf-pro\",\n                        style: {\n                            color: colors.text.secondary,\n                            fontSize: \"18px\",\n                            margin: 0,\n                            fontWeight: \"400\",\n                            opacity: 0.8\n                        },\n                        children: \"Your daily mission control center\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    maxWidth: \"1000px\",\n                    margin: \"0 auto\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: colors.surface,\n                            borderRadius: \"20px\",\n                            padding: \"40px\",\n                            marginBottom: \"30px\",\n                            boxShadow: \"\\n            0 8px 32px rgba(0, 0, 0, 0.06),\\n            0 2px 8px rgba(0, 0, 0, 0.04),\\n            inset 0 1px 0 rgba(255, 255, 255, 0.9)\\n          \",\n                            border: \"1px solid \".concat(colors.border),\n                            position: \"relative\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    position: \"absolute\",\n                                    top: 0,\n                                    left: 0,\n                                    right: 0,\n                                    height: \"100px\",\n                                    background: \"radial-gradient(ellipse at top, \".concat(colors.warmGlow, \"15 0%, transparent 70%)\"),\n                                    pointerEvents: \"none\",\n                                    borderRadius: \"20px 20px 0 0\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    position: \"relative\",\n                                    zIndex: 1\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            justifyContent: \"space-between\",\n                                            marginBottom: \"20px\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: \"flex\",\n                                                    alignItems: \"center\",\n                                                    gap: \"12px\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            width: \"40px\",\n                                                            height: \"40px\",\n                                                            borderRadius: \"10px\",\n                                                            background: \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\"),\n                                                            display: \"flex\",\n                                                            alignItems: \"center\",\n                                                            justifyContent: \"center\",\n                                                            boxShadow: \"0 4px 12px \".concat(colors.primary, \"30\")\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_Sparkles_Target_TrendingUp_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Target, {\n                                                            size: 20,\n                                                            color: \"white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                            lineNumber: 101,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                        lineNumber: 91,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"elegant-cursive\",\n                                                        style: {\n                                                            color: colors.text.primary,\n                                                            margin: 0,\n                                                            fontSize: \"28px\",\n                                                            fontWeight: \"400\"\n                                                        },\n                                                        children: \"Today's Mission\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                        lineNumber: 103,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                lineNumber: 90,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: \"flex\",\n                                                    alignItems: \"center\",\n                                                    gap: \"6px\",\n                                                    padding: \"6px 12px\",\n                                                    background: \"\".concat(colors.primary, \"15\"),\n                                                    borderRadius: \"20px\",\n                                                    border: \"1px solid \".concat(colors.primary, \"25\")\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_Sparkles_Target_TrendingUp_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Sparkles, {\n                                                        size: 12,\n                                                        color: colors.primary\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                        lineNumber: 121,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"sf-pro\",\n                                                        style: {\n                                                            color: colors.primary,\n                                                            fontSize: \"11px\",\n                                                            fontWeight: \"600\",\n                                                            textTransform: \"uppercase\",\n                                                            letterSpacing: \"0.5px\"\n                                                        },\n                                                        children: \"AI Generated\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                        lineNumber: 122,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"sf-pro\",\n                                        style: {\n                                            color: colors.text.secondary,\n                                            fontSize: \"17px\",\n                                            lineHeight: \"1.6\",\n                                            margin: 0,\n                                            marginBottom: \"30px\"\n                                        },\n                                        children: \"Focus on engaging with your audience about AI productivity tools. Your recent posts about automation got 3x more engagement. Consider sharing a behind-the-scenes story about how AI helps your workflow.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"grid\",\n                                            gridTemplateColumns: \"repeat(3, 1fr)\",\n                                            gap: \"20px\",\n                                            marginBottom: \"30px\"\n                                        },\n                                        children: [\n                                            {\n                                                label: \"Engagement Rate\",\n                                                value: \"+24%\",\n                                                icon: _barrel_optimize_names_MessageCircle_Sparkles_Target_TrendingUp_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.TrendingUp,\n                                                color: \"#10B981\"\n                                            },\n                                            {\n                                                label: \"New Followers\",\n                                                value: \"127\",\n                                                icon: _barrel_optimize_names_MessageCircle_Sparkles_Target_TrendingUp_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Users,\n                                                color: \"#3B82F6\"\n                                            },\n                                            {\n                                                label: \"Content Score\",\n                                                value: \"8.9/10\",\n                                                icon: _barrel_optimize_names_MessageCircle_Sparkles_Target_TrendingUp_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Target,\n                                                color: colors.primary\n                                            }\n                                        ].map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    background: colors.surface,\n                                                    borderRadius: \"16px\",\n                                                    padding: \"24px\",\n                                                    textAlign: \"center\",\n                                                    border: \"1px solid \".concat(colors.border),\n                                                    boxShadow: \"0 4px 12px rgba(0, 0, 0, 0.04)\",\n                                                    position: \"relative\",\n                                                    overflow: \"hidden\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            position: \"absolute\",\n                                                            top: \"-10px\",\n                                                            right: \"-10px\",\n                                                            width: \"40px\",\n                                                            height: \"40px\",\n                                                            borderRadius: \"50%\",\n                                                            background: \"\".concat(stat.color, \"15\"),\n                                                            display: \"flex\",\n                                                            alignItems: \"center\",\n                                                            justifyContent: \"center\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(stat.icon, {\n                                                            size: 16,\n                                                            color: stat.color,\n                                                            style: {\n                                                                opacity: 0.6\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                            lineNumber: 179,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                        lineNumber: 167,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"sf-pro\",\n                                                        style: {\n                                                            fontSize: \"28px\",\n                                                            fontWeight: \"700\",\n                                                            color: colors.text.primary,\n                                                            marginBottom: \"8px\"\n                                                        },\n                                                        children: stat.value\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                        lineNumber: 182,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"sf-pro\",\n                                                        style: {\n                                                            fontSize: \"14px\",\n                                                            color: colors.text.tertiary,\n                                                            fontWeight: \"500\"\n                                                        },\n                                                        children: stat.label\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                        lineNumber: 190,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                lineNumber: 156,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            gap: \"16px\",\n                                            flexWrap: \"wrap\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/meeting\",\n                                                style: {\n                                                    textDecoration: \"none\"\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"sf-pro\",\n                                                    style: {\n                                                        display: \"flex\",\n                                                        alignItems: \"center\",\n                                                        gap: \"8px\",\n                                                        padding: \"14px 24px\",\n                                                        background: \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\"),\n                                                        color: \"white\",\n                                                        border: \"none\",\n                                                        borderRadius: \"12px\",\n                                                        fontSize: \"14px\",\n                                                        fontWeight: \"600\",\n                                                        cursor: \"pointer\",\n                                                        transition: \"all 0.2s ease\",\n                                                        boxShadow: \"0 4px 16px \".concat(colors.primary, \"30\")\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_Sparkles_Target_TrendingUp_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Video, {\n                                                            size: 16\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                            lineNumber: 223,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        \"Join Call\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                    lineNumber: 208,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"sf-pro\",\n                                                style: {\n                                                    display: \"flex\",\n                                                    alignItems: \"center\",\n                                                    gap: \"8px\",\n                                                    padding: \"14px 24px\",\n                                                    background: colors.surface,\n                                                    color: colors.text.primary,\n                                                    border: \"1px solid \".concat(colors.border),\n                                                    borderRadius: \"12px\",\n                                                    fontSize: \"14px\",\n                                                    fontWeight: \"600\",\n                                                    cursor: \"pointer\",\n                                                    transition: \"all 0.2s ease\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_Sparkles_Target_TrendingUp_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Sparkles, {\n                                                        size: 16\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                        lineNumber: 242,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \"Ask Mentor\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/tweet-center\",\n                                                style: {\n                                                    textDecoration: \"none\"\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"sf-pro\",\n                                                    style: {\n                                                        display: \"flex\",\n                                                        alignItems: \"center\",\n                                                        gap: \"8px\",\n                                                        padding: \"14px 24px\",\n                                                        background: colors.surface,\n                                                        color: colors.text.primary,\n                                                        border: \"1px solid \".concat(colors.border),\n                                                        borderRadius: \"12px\",\n                                                        fontSize: \"14px\",\n                                                        fontWeight: \"600\",\n                                                        cursor: \"pointer\",\n                                                        transition: \"all 0.2s ease\"\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_Sparkles_Target_TrendingUp_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.MessageCircle, {\n                                                            size: 16\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                            lineNumber: 261,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        \"Create Content\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                    lineNumber: 247,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: colors.surface,\n                            borderRadius: \"16px\",\n                            padding: \"24px\",\n                            boxShadow: \"0 4px 16px rgba(0, 0, 0, 0.04)\",\n                            border: \"1px solid \".concat(colors.border)\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: \"flex\",\n                                alignItems: \"flex-start\",\n                                gap: \"16px\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        width: \"48px\",\n                                        height: \"48px\",\n                                        borderRadius: \"12px\",\n                                        background: \"\".concat(colors.primary, \"15\"),\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        justifyContent: \"center\",\n                                        position: \"relative\",\n                                        flexShrink: 0\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: \"24px\",\n                                                height: \"24px\",\n                                                borderRadius: \"6px\",\n                                                background: colors.primary\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                position: \"absolute\",\n                                                bottom: \"2px\",\n                                                right: \"2px\",\n                                                width: \"12px\",\n                                                height: \"12px\",\n                                                borderRadius: \"50%\",\n                                                background: \"#00E676\",\n                                                border: \"2px solid white\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                            lineNumber: 295,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        flex: 1\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            style: {\n                                                color: colors.text.primary,\n                                                margin: 0,\n                                                fontSize: \"16px\",\n                                                fontWeight: \"600\",\n                                                marginBottom: \"8px\"\n                                            },\n                                            children: \"AI Mentor\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                color: colors.text.secondary,\n                                                margin: 0,\n                                                fontSize: \"15px\",\n                                                lineHeight: \"1.5\"\n                                            },\n                                            children: \"Ready to help you create content that resonates. What's on your mind today?\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                            lineNumber: 277,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                        lineNumber: 270,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, undefined);\n};\n_c = HomePage;\nHomePage.getLayout = function getLayout(page) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SidebarLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: page\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n        lineNumber: 334,\n        columnNumber: 5\n    }, this);\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (HomePage);\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/index.tsx\n"));

/***/ })

});